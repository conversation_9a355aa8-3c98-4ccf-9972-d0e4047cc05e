# SIMA Portal - Smart IT Maintenance Android App

A comprehensive Android application for managing IT maintenance requests with role-based access control for Users, Specialists, and Administrators.

## Features

### 🔐 **Authentication System**
- Firebase Authentication integration
- Role-based user registration (User, Specialist, Admin)
- Secure session management
- Automatic role-based navigation

### 👥 **User Management**
- User profile management
- Specialist category system (Software, Network, Hardware)
- Admin approval workflow for specialists

### 📋 **Maintenance Request System**
- Create and manage maintenance posts
- Category-based filtering
- Priority levels (Low, Medium, High, Urgent)
- Budget range specification
- Location and requirements tracking

### 🏢 **Admin Panel**
- System dashboard with statistics
- User management with filtering
- Specialist approval system
- Post monitoring and management
- Comprehensive analytics

### 🔧 **Specialist Features**
- Category-specific post filtering
- Application management system
- Rating and job completion tracking
- Availability status management

## Tech Stack

- **Language**: Java
- **Architecture**: MVVM with Repository Pattern
- **Database**: Room (SQLite) + Firebase Firestore
- **Authentication**: Firebase Auth
- **UI**: Material Design 3 Components
- **Networking**: Retrofit + OkHttp
- **Image Loading**: Glide
- **Build System**: Gradle with Version Catalogs

## Build Requirements

- **Android Studio**: Arctic Fox or later
- **Minimum SDK**: 24 (Android 7.0)
- **Target SDK**: 34 (Android 14)
- **Java Version**: 8
- **Gradle**: 8.7.3

## Setup Instructions

### 1. Clone the Repository
```bash
git clone <repository-url>
cd sima-portal
```

### 2. Firebase Setup
1. Create a new Firebase project at [Firebase Console](https://console.firebase.google.com/)
2. Add an Android app with package name: `com.example.simaportal`
3. Download `google-services.json` and replace the demo file in `app/` directory
4. Enable Authentication and Firestore in Firebase Console

### 3. Build the Project
```bash
# Clean and build
./gradlew clean build

# Install debug APK
./gradlew installDebug

# Run tests
./gradlew test
```

### 4. Run the Application
1. Open the project in Android Studio
2. Sync the project with Gradle files
3. Run the app on an emulator or physical device

## Project Structure

```
app/
├── src/main/java/com/example/simaportal/
│   ├── activities/          # Activity classes
│   │   ├── admin/          # Admin-specific activities
│   │   ├── specialist/     # Specialist-specific activities
│   │   └── user/           # User-specific activities
│   ├── adapters/           # RecyclerView adapters
│   ├── auth/               # Authentication management
│   ├── database/           # Room database components
│   │   ├── dao/            # Data Access Objects
│   │   └── entities/       # Database entities
│   └── fragments/          # Fragment classes
│       ├── admin/          # Admin fragments
│       ├── specialist/     # Specialist fragments
│       └── user/           # User fragments
├── src/main/res/
│   ├── layout/             # XML layouts
│   ├── drawable/           # Icons and graphics
│   ├── values/             # Colors, strings, themes
│   └── menu/               # Navigation menus
└── google-services.json    # Firebase configuration
```

## Database Schema

### Core Entities
- **User**: Basic user information and authentication
- **Specialist**: Extended user data for specialists
- **MaintenancePost**: Maintenance request details
- **Application**: Specialist applications to posts

### Key Relationships
- User (1) → Specialist (0..1)
- User (1) → MaintenancePost (0..*)
- Specialist (1) → Application (0..*)
- MaintenancePost (1) → Application (0..*)

## Default User Roles

The app supports three user roles:

1. **USER**: Can create and manage maintenance requests
2. **SPECIALIST**: Can view and apply to relevant maintenance posts
3. **ADMIN**: Full system management capabilities

## Build Configuration

### Gradle Files
- `app/build.gradle`: App-level build configuration
- `build.gradle`: Project-level build configuration
- `gradle/libs.versions.toml`: Version catalog for dependencies
- `settings.gradle`: Project settings

### Key Dependencies
- Room Database: 2.6.1
- Firebase BOM: 33.7.0
- Material Components: 1.12.0
- Retrofit: 2.11.0
- Glide: 4.16.0

## ProGuard Configuration

The app includes comprehensive ProGuard rules for:
- Room Database entities
- Firebase components
- Retrofit networking
- Gson serialization
- Glide image loading

## Testing

Run the test suite:
```bash
# Unit tests
./gradlew test

# Instrumented tests
./gradlew connectedAndroidTest
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions, please create an issue in the repository or contact the development team.
