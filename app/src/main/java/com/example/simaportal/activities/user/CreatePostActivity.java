package com.example.simaportal.activities.user;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

import com.example.simaportal.auth.AuthManager;
import com.example.simaportal.database.SIMADatabase;
import com.example.simaportal.database.entities.MaintenancePost;
import com.example.simaportal.databinding.ActivityCreatePostBinding;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class CreatePostActivity extends AppCompatActivity {

    private ActivityCreatePostBinding binding;
    private AuthManager authManager;
    private SIMADatabase database;
    private ExecutorService executorService;
    private String selectedCategory = "";
    private String selectedPriority = "";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityCreatePostBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        authManager = AuthManager.getInstance(this);
        database = SIMADatabase.getInstance(this);
        executorService = Executors.newFixedThreadPool(2);

        setupUI();
        setupSpinners();
    }

    private void setupUI() {
        setSupportActionBar(binding.toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setTitle("Create Maintenance Request");
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        }

        binding.btnSubmit.setOnClickListener(v -> handleSubmit());
        binding.btnCancel.setOnClickListener(v -> finish());
    }

    private void setupSpinners() {
        // Category spinner
        String[] categories = {"Select Category", "Software", "Network", "Hardware"};
        ArrayAdapter<String> categoryAdapter = new ArrayAdapter<>(this, 
                android.R.layout.simple_spinner_item, categories);
        categoryAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        binding.spinnerCategory.setAdapter(categoryAdapter);

        binding.spinnerCategory.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                if (position > 0) {
                    selectedCategory = categories[position].toUpperCase();
                } else {
                    selectedCategory = "";
                }
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {
                selectedCategory = "";
            }
        });

        // Priority spinner
        String[] priorities = {"Select Priority", "Low", "Medium", "High", "Urgent"};
        ArrayAdapter<String> priorityAdapter = new ArrayAdapter<>(this, 
                android.R.layout.simple_spinner_item, priorities);
        priorityAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        binding.spinnerPriority.setAdapter(priorityAdapter);

        binding.spinnerPriority.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                if (position > 0) {
                    selectedPriority = priorities[position].toUpperCase();
                } else {
                    selectedPriority = "";
                }
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {
                selectedPriority = "";
            }
        });
    }

    private void handleSubmit() {
        String title = binding.etTitle.getText().toString().trim();
        String description = binding.etDescription.getText().toString().trim();
        String location = binding.etLocation.getText().toString().trim();
        String requirements = binding.etRequirements.getText().toString().trim();
        String contactPhone = binding.etContactPhone.getText().toString().trim();
        String contactEmail = binding.etContactEmail.getText().toString().trim();

        if (!validateInput(title, description)) {
            return;
        }

        showLoading();

        int currentUserId = authManager.getCurrentUserId();
        if (currentUserId == -1) {
            hideLoading();
            Toast.makeText(this, "Error: Unable to get current user", Toast.LENGTH_SHORT).show();
            return;
        }

        executorService.execute(() -> {
            try {
                MaintenancePost post = new MaintenancePost(currentUserId, title, description, selectedCategory);
                post.setPriority(selectedPriority);
                post.setLocation(location);
                post.setRequirements(requirements);
                post.setContactPhone(contactPhone);
                post.setContactEmail(contactEmail);
                post.setUrgent("URGENT".equals(selectedPriority));

                // Handle budget if provided
                String budgetMinStr = binding.etBudgetMin.getText().toString().trim();
                String budgetMaxStr = binding.etBudgetMax.getText().toString().trim();
                
                if (!TextUtils.isEmpty(budgetMinStr)) {
                    try {
                        post.setBudgetMin(Double.parseDouble(budgetMinStr));
                    } catch (NumberFormatException e) {
                        // Ignore invalid budget
                    }
                }
                
                if (!TextUtils.isEmpty(budgetMaxStr)) {
                    try {
                        post.setBudgetMax(Double.parseDouble(budgetMaxStr));
                    } catch (NumberFormatException e) {
                        // Ignore invalid budget
                    }
                }

                database.maintenancePostDao().insert(post);

                runOnUiThread(() -> {
                    hideLoading();
                    Toast.makeText(this, "Maintenance request created successfully!", Toast.LENGTH_SHORT).show();
                    finish();
                });

            } catch (Exception e) {
                runOnUiThread(() -> {
                    hideLoading();
                    Toast.makeText(this, "Error creating post: " + e.getMessage(), Toast.LENGTH_SHORT).show();
                });
            }
        });
    }

    private boolean validateInput(String title, String description) {
        if (TextUtils.isEmpty(title)) {
            binding.etTitle.setError("Title is required");
            binding.etTitle.requestFocus();
            return false;
        }

        if (TextUtils.isEmpty(description)) {
            binding.etDescription.setError("Description is required");
            binding.etDescription.requestFocus();
            return false;
        }

        if (TextUtils.isEmpty(selectedCategory)) {
            Toast.makeText(this, "Please select a category", Toast.LENGTH_SHORT).show();
            return false;
        }

        if (TextUtils.isEmpty(selectedPriority)) {
            Toast.makeText(this, "Please select a priority", Toast.LENGTH_SHORT).show();
            return false;
        }

        return true;
    }

    private void showLoading() {
        binding.progressBar.setVisibility(View.VISIBLE);
        binding.btnSubmit.setEnabled(false);
        binding.btnSubmit.setText("Creating...");
    }

    private void hideLoading() {
        binding.progressBar.setVisibility(View.GONE);
        binding.btnSubmit.setEnabled(true);
        binding.btnSubmit.setText("Create Post");
    }

    @Override
    public boolean onSupportNavigateUp() {
        finish();
        return true;
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
        }
        binding = null;
    }
}
