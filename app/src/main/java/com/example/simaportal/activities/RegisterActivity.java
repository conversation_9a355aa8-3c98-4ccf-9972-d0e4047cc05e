package com.example.simaportal.activities;

import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

import com.example.simaportal.R;
import com.example.simaportal.auth.AuthManager;
import com.example.simaportal.database.entities.User;
import com.example.simaportal.databinding.ActivityRegisterBinding;

public class RegisterActivity extends AppCompatActivity {

    private ActivityRegisterBinding binding;
    private AuthManager authManager;
    private String selectedRole = "";
    private String selectedSpecialistCategory = "";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityRegisterBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        authManager = AuthManager.getInstance(this);

        setupUI();
        setupSpinners();
        observeAuthState();
    }

    private void setupUI() {
        binding.btnRegister.setOnClickListener(v -> handleRegistration());
        binding.tvSignIn.setOnClickListener(v -> navigateToLogin());
        binding.btnBack.setOnClickListener(v -> finish());
    }

    private void setupSpinners() {
        // Setup role spinner
        String[] roles = {"Select Role", "User", "Specialist"};
        ArrayAdapter<String> roleAdapter = new ArrayAdapter<>(this, 
                android.R.layout.simple_spinner_item, roles);
        roleAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        binding.spinnerRole.setAdapter(roleAdapter);

        binding.spinnerRole.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                if (position > 0) {
                    selectedRole = roles[position].toUpperCase();
                    
                    // Show specialist category spinner if specialist is selected
                    if ("SPECIALIST".equals(selectedRole)) {
                        binding.layoutSpecialistCategory.setVisibility(View.VISIBLE);
                        setupSpecialistCategorySpinner();
                    } else {
                        binding.layoutSpecialistCategory.setVisibility(View.GONE);
                        selectedSpecialistCategory = "";
                    }
                } else {
                    selectedRole = "";
                    binding.layoutSpecialistCategory.setVisibility(View.GONE);
                }
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {
                selectedRole = "";
                binding.layoutSpecialistCategory.setVisibility(View.GONE);
            }
        });
    }

    private void setupSpecialistCategorySpinner() {
        String[] categories = {"Select Category", "Software", "Network", "Hardware"};
        ArrayAdapter<String> categoryAdapter = new ArrayAdapter<>(this, 
                android.R.layout.simple_spinner_item, categories);
        categoryAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        binding.spinnerSpecialistCategory.setAdapter(categoryAdapter);

        binding.spinnerSpecialistCategory.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                if (position > 0) {
                    selectedSpecialistCategory = categories[position].toUpperCase();
                } else {
                    selectedSpecialistCategory = "";
                }
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {
                selectedSpecialistCategory = "";
            }
        });
    }

    private void observeAuthState() {
        authManager.getIsLoggedInLiveData().observe(this, isLoggedIn -> {
            if (isLoggedIn != null && isLoggedIn) {
                navigateToMainActivity();
            }
        });

        authManager.getAuthErrorLiveData().observe(this, error -> {
            if (error != null && !error.isEmpty()) {
                hideLoading();
                Toast.makeText(this, error, Toast.LENGTH_LONG).show();
            }
        });
    }

    private void handleRegistration() {
        String firstName = binding.etFirstName.getText().toString().trim();
        String lastName = binding.etLastName.getText().toString().trim();
        String email = binding.etEmail.getText().toString().trim();
        String password = binding.etPassword.getText().toString().trim();
        String confirmPassword = binding.etConfirmPassword.getText().toString().trim();

        if (!validateInput(firstName, lastName, email, password, confirmPassword)) {
            return;
        }

        showLoading();
        authManager.signUp(email, password, firstName, lastName, selectedRole, 
                          selectedSpecialistCategory, new AuthManager.AuthCallback() {
            @Override
            public void onSuccess(User user) {
                hideLoading();
                Toast.makeText(RegisterActivity.this, "Registration successful!", Toast.LENGTH_SHORT).show();
                navigateToMainActivity();
            }

            @Override
            public void onFailure(String error) {
                hideLoading();
                Toast.makeText(RegisterActivity.this, error, Toast.LENGTH_LONG).show();
            }
        });
    }

    private boolean validateInput(String firstName, String lastName, String email, 
                                String password, String confirmPassword) {
        if (TextUtils.isEmpty(firstName)) {
            binding.etFirstName.setError("First name is required");
            binding.etFirstName.requestFocus();
            return false;
        }

        if (TextUtils.isEmpty(lastName)) {
            binding.etLastName.setError("Last name is required");
            binding.etLastName.requestFocus();
            return false;
        }

        if (TextUtils.isEmpty(email)) {
            binding.etEmail.setError("Email is required");
            binding.etEmail.requestFocus();
            return false;
        }

        if (!android.util.Patterns.EMAIL_ADDRESS.matcher(email).matches()) {
            binding.etEmail.setError("Please enter a valid email");
            binding.etEmail.requestFocus();
            return false;
        }

        if (TextUtils.isEmpty(password)) {
            binding.etPassword.setError("Password is required");
            binding.etPassword.requestFocus();
            return false;
        }

        if (password.length() < 6) {
            binding.etPassword.setError("Password must be at least 6 characters");
            binding.etPassword.requestFocus();
            return false;
        }

        if (!password.equals(confirmPassword)) {
            binding.etConfirmPassword.setError("Passwords do not match");
            binding.etConfirmPassword.requestFocus();
            return false;
        }

        if (TextUtils.isEmpty(selectedRole)) {
            Toast.makeText(this, "Please select a role", Toast.LENGTH_SHORT).show();
            return false;
        }

        if ("SPECIALIST".equals(selectedRole) && TextUtils.isEmpty(selectedSpecialistCategory)) {
            Toast.makeText(this, "Please select a specialist category", Toast.LENGTH_SHORT).show();
            return false;
        }

        return true;
    }

    private void navigateToLogin() {
        finish();
    }

    private void navigateToMainActivity() {
        Intent intent = new Intent(this, MainActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
        startActivity(intent);
        finish();
    }

    private void showLoading() {
        binding.progressBar.setVisibility(View.VISIBLE);
        binding.btnRegister.setEnabled(false);
        binding.btnRegister.setText("Creating Account...");
    }

    private void hideLoading() {
        binding.progressBar.setVisibility(View.GONE);
        binding.btnRegister.setEnabled(true);
        binding.btnRegister.setText("Create Account");
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        binding = null;
    }
}
