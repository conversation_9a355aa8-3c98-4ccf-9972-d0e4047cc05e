package com.example.simaportal.activities;

import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

import com.example.simaportal.auth.AuthManager;
import com.example.simaportal.database.entities.User;
import com.example.simaportal.databinding.ActivityLoginBinding;

public class LoginActivity extends AppCompatActivity {

    private ActivityLoginBinding binding;
    private AuthManager authManager;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityLoginBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        authManager = AuthManager.getInstance(this);

        setupUI();
        observeAuthState();
    }

    private void setupUI() {
        binding.btnLogin.setOnClickListener(v -> handleLogin());
        binding.tvSignUp.setOnClickListener(v -> navigateToSignUp());
        binding.tvForgotPassword.setOnClickListener(v -> handleForgotPassword());
    }

    private void observeAuthState() {
        authManager.getIsLoggedInLiveData().observe(this, isLoggedIn -> {
            if (isLoggedIn != null && isLoggedIn) {
                navigateToMainActivity();
            }
        });

        authManager.getAuthErrorLiveData().observe(this, error -> {
            if (error != null && !error.isEmpty()) {
                hideLoading();
                Toast.makeText(this, error, Toast.LENGTH_LONG).show();
            }
        });
    }

    private void handleLogin() {
        String email = binding.etEmail.getText().toString().trim();
        String password = binding.etPassword.getText().toString().trim();

        if (!validateInput(email, password)) {
            return;
        }

        showLoading();
        authManager.signIn(email, password, new AuthManager.AuthCallback() {
            @Override
            public void onSuccess(User user) {
                hideLoading();
                navigateToMainActivity();
            }

            @Override
            public void onFailure(String error) {
                hideLoading();
                Toast.makeText(LoginActivity.this, error, Toast.LENGTH_LONG).show();
            }
        });
    }

    private boolean validateInput(String email, String password) {
        if (TextUtils.isEmpty(email)) {
            binding.etEmail.setError("Email is required");
            binding.etEmail.requestFocus();
            return false;
        }

        if (!android.util.Patterns.EMAIL_ADDRESS.matcher(email).matches()) {
            binding.etEmail.setError("Please enter a valid email");
            binding.etEmail.requestFocus();
            return false;
        }

        if (TextUtils.isEmpty(password)) {
            binding.etPassword.setError("Password is required");
            binding.etPassword.requestFocus();
            return false;
        }

        if (password.length() < 6) {
            binding.etPassword.setError("Password must be at least 6 characters");
            binding.etPassword.requestFocus();
            return false;
        }

        return true;
    }

    private void handleForgotPassword() {
        String email = binding.etEmail.getText().toString().trim();
        
        if (TextUtils.isEmpty(email)) {
            binding.etEmail.setError("Please enter your email first");
            binding.etEmail.requestFocus();
            return;
        }

        if (!android.util.Patterns.EMAIL_ADDRESS.matcher(email).matches()) {
            binding.etEmail.setError("Please enter a valid email");
            binding.etEmail.requestFocus();
            return;
        }

        // TODO: Implement forgot password functionality
        Toast.makeText(this, "Password reset functionality will be implemented", Toast.LENGTH_SHORT).show();
    }

    private void navigateToSignUp() {
        Intent intent = new Intent(this, RegisterActivity.class);
        startActivity(intent);
    }

    private void navigateToMainActivity() {
        Intent intent = new Intent(this, MainActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
        startActivity(intent);
        finish();
    }

    private void showLoading() {
        binding.progressBar.setVisibility(View.VISIBLE);
        binding.btnLogin.setEnabled(false);
        binding.btnLogin.setText("Signing in...");
    }

    private void hideLoading() {
        binding.progressBar.setVisibility(View.GONE);
        binding.btnLogin.setEnabled(true);
        binding.btnLogin.setText("Sign In");
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        binding = null;
    }
}
