package com.example.simaportal.activities.user;

import android.content.Intent;
import android.os.Bundle;
import android.view.MenuItem;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.fragment.app.Fragment;

import com.example.simaportal.R;
import com.example.simaportal.activities.LoginActivity;
import com.example.simaportal.auth.AuthManager;
import com.example.simaportal.databinding.ActivityUserDashboardBinding;
import com.example.simaportal.fragments.user.UserHomeFragment;
import com.example.simaportal.fragments.user.UserPostsFragment;
import com.example.simaportal.fragments.user.UserProfileFragment;
import com.google.android.material.navigation.NavigationBarView;

public class UserDashboardActivity extends AppCompatActivity implements NavigationBarView.OnItemSelectedListener {

    private ActivityUserDashboardBinding binding;
    private AuthManager authManager;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityUserDashboardBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        authManager = AuthManager.getInstance(this);

        setupUI();
        setupNavigation();
        
        // Load default fragment
        if (savedInstanceState == null) {
            loadFragment(new UserHomeFragment());
        }
    }

    private void setupUI() {
        setSupportActionBar(binding.toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setTitle("User Dashboard");
        }
    }

    private void setupNavigation() {
        binding.bottomNavigation.setOnItemSelectedListener(this);
        binding.bottomNavigation.setSelectedItemId(R.id.nav_home);
    }

    @Override
    public boolean onNavigationItemSelected(@NonNull MenuItem item) {
        Fragment fragment = null;
        String title = "";

        int itemId = item.getItemId();
        if (itemId == R.id.nav_home) {
            fragment = new UserHomeFragment();
            title = "Home";
        } else if (itemId == R.id.nav_posts) {
            fragment = new UserPostsFragment();
            title = "My Posts";
        } else if (itemId == R.id.nav_profile) {
            fragment = new UserProfileFragment();
            title = "Profile";
        }

        if (fragment != null) {
            loadFragment(fragment);
            if (getSupportActionBar() != null) {
                getSupportActionBar().setTitle(title);
            }
            return true;
        }
        return false;
    }

    private void loadFragment(Fragment fragment) {
        getSupportFragmentManager()
                .beginTransaction()
                .replace(R.id.fragment_container, fragment)
                .commit();
    }

    public void logout() {
        authManager.signOut();
        Intent intent = new Intent(this, LoginActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
        startActivity(intent);
        finish();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        binding = null;
    }
}
