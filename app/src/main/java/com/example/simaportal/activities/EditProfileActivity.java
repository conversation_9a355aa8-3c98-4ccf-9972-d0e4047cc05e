package com.example.simaportal.activities;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

import com.example.simaportal.auth.AuthManager;
import com.example.simaportal.database.SIMADatabase;
import com.example.simaportal.database.entities.User;
import com.example.simaportal.databinding.ActivityEditProfileBinding;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class EditProfileActivity extends AppCompatActivity {

    private ActivityEditProfileBinding binding;
    private AuthManager authManager;
    private SIMADatabase database;
    private ExecutorService executorService;
    private User currentUser;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityEditProfileBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        authManager = AuthManager.getInstance(this);
        database = SIMADatabase.getInstance(this);
        executorService = Executors.newFixedThreadPool(2);

        setupUI();
        loadUserData();
    }

    private void setupUI() {
        setSupportActionBar(binding.toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setTitle("Edit Profile");
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        }

        binding.btnSave.setOnClickListener(v -> handleSave());
        binding.btnCancel.setOnClickListener(v -> finish());
    }

    private void loadUserData() {
        authManager.getCurrentUserLiveData().observe(this, user -> {
            if (user != null) {
                currentUser = user;
                populateFields(user);
            }
        });
    }

    private void populateFields(User user) {
        binding.etFirstName.setText(user.getFirstName());
        binding.etLastName.setText(user.getLastName());
        binding.etEmail.setText(user.getEmail());
        
        if (user.getPhoneNumber() != null) {
            binding.etPhoneNumber.setText(user.getPhoneNumber());
        }
        
        if (user.getDateOfBirth() != null) {
            binding.etDateOfBirth.setText(user.getDateOfBirth());
        }
    }

    private void handleSave() {
        String firstName = binding.etFirstName.getText().toString().trim();
        String lastName = binding.etLastName.getText().toString().trim();
        String email = binding.etEmail.getText().toString().trim();
        String phoneNumber = binding.etPhoneNumber.getText().toString().trim();
        String dateOfBirth = binding.etDateOfBirth.getText().toString().trim();

        if (!validateInput(firstName, lastName, email)) {
            return;
        }

        showLoading();

        executorService.execute(() -> {
            try {
                long currentTime = System.currentTimeMillis();
                
                // Update user name
                database.userDao().updateUserName(currentUser.getId(), firstName, lastName, currentTime);
                
                // Update email if changed
                if (!email.equals(currentUser.getEmail())) {
                    database.userDao().updateUserEmail(currentUser.getId(), email, currentTime);
                }
                
                // Update phone number
                database.userDao().updateUserPhone(currentUser.getId(), phoneNumber, currentTime);
                
                // Update date of birth
                database.userDao().updateUserDateOfBirth(currentUser.getId(), dateOfBirth, currentTime);

                runOnUiThread(() -> {
                    hideLoading();
                    Toast.makeText(this, "Profile updated successfully!", Toast.LENGTH_SHORT).show();
                    finish();
                });

            } catch (Exception e) {
                runOnUiThread(() -> {
                    hideLoading();
                    Toast.makeText(this, "Error updating profile: " + e.getMessage(), Toast.LENGTH_SHORT).show();
                });
            }
        });
    }

    private boolean validateInput(String firstName, String lastName, String email) {
        if (TextUtils.isEmpty(firstName)) {
            binding.etFirstName.setError("First name is required");
            binding.etFirstName.requestFocus();
            return false;
        }

        if (TextUtils.isEmpty(lastName)) {
            binding.etLastName.setError("Last name is required");
            binding.etLastName.requestFocus();
            return false;
        }

        if (TextUtils.isEmpty(email)) {
            binding.etEmail.setError("Email is required");
            binding.etEmail.requestFocus();
            return false;
        }

        if (!android.util.Patterns.EMAIL_ADDRESS.matcher(email).matches()) {
            binding.etEmail.setError("Please enter a valid email");
            binding.etEmail.requestFocus();
            return false;
        }

        return true;
    }

    private void showLoading() {
        binding.progressBar.setVisibility(View.VISIBLE);
        binding.btnSave.setEnabled(false);
        binding.btnSave.setText("Saving...");
    }

    private void hideLoading() {
        binding.progressBar.setVisibility(View.GONE);
        binding.btnSave.setEnabled(true);
        binding.btnSave.setText("Save Changes");
    }

    @Override
    public boolean onSupportNavigateUp() {
        finish();
        return true;
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
        }
        binding = null;
    }
}
