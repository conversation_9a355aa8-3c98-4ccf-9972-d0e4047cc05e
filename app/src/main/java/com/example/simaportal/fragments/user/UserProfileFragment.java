package com.example.simaportal.fragments.user;

import android.content.Intent;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.example.simaportal.activities.user.UserDashboardActivity;
import com.example.simaportal.activities.EditProfileActivity;
import com.example.simaportal.auth.AuthManager;
import com.example.simaportal.databinding.FragmentUserProfileBinding;
import com.example.simaportal.database.entities.User;

public class UserProfileFragment extends Fragment {

    private FragmentUserProfileBinding binding;
    private AuthManager authManager;

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        binding = FragmentUserProfileBinding.inflate(inflater, container, false);
        return binding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        
        authManager = AuthManager.getInstance(requireContext());
        
        setupUI();
        observeUser();
    }

    private void setupUI() {
        binding.btnEditProfile.setOnClickListener(v -> {
            Intent intent = new Intent(getContext(), EditProfileActivity.class);
            startActivity(intent);
        });

        binding.btnLogout.setOnClickListener(v -> {
            if (getActivity() instanceof UserDashboardActivity) {
                ((UserDashboardActivity) getActivity()).logout();
            }
        });
    }

    private void observeUser() {
        authManager.getCurrentUserLiveData().observe(getViewLifecycleOwner(), user -> {
            if (user != null) {
                updateUI(user);
            }
        });
    }

    private void updateUI(User user) {
        binding.tvUserName.setText(user.getFullName());
        binding.tvUserEmail.setText(user.getEmail());
        binding.tvUserRole.setText(user.getRole());
        
        if (user.getPhoneNumber() != null && !user.getPhoneNumber().isEmpty()) {
            binding.tvUserPhone.setText(user.getPhoneNumber());
            binding.tvUserPhone.setVisibility(View.VISIBLE);
        } else {
            binding.tvUserPhone.setVisibility(View.GONE);
        }
        
        if (user.getDateOfBirth() != null && !user.getDateOfBirth().isEmpty()) {
            binding.tvUserDob.setText(user.getDateOfBirth());
            binding.tvUserDob.setVisibility(View.VISIBLE);
        } else {
            binding.tvUserDob.setVisibility(View.GONE);
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        binding = null;
    }
}
