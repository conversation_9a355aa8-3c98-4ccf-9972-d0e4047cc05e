package com.example.simaportal.fragments.admin;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.example.simaportal.adapters.PostsAdapter;
import com.example.simaportal.databinding.FragmentAdminPostsBinding;
import com.example.simaportal.database.SIMADatabase;
import com.example.simaportal.database.entities.MaintenancePost;

import java.util.ArrayList;
import java.util.List;

public class AdminPostsFragment extends Fragment implements PostsAdapter.OnPostActionListener {

    private FragmentAdminPostsBinding binding;
    private SIMADatabase database;
    private PostsAdapter postsAdapter;
    private String currentFilter = "ALL";

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        binding = FragmentAdminPostsBinding.inflate(inflater, container, false);
        return binding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        
        database = SIMADatabase.getInstance(requireContext());
        
        setupRecyclerView();
        setupFilters();
        loadPosts();
    }

    private void setupRecyclerView() {
        postsAdapter = new PostsAdapter(new ArrayList<>(), this, true); // true for admin mode
        binding.recyclerViewPosts.setLayoutManager(new LinearLayoutManager(getContext()));
        binding.recyclerViewPosts.setAdapter(postsAdapter);
    }

    private void setupFilters() {
        binding.chipAll.setOnClickListener(v -> filterPosts("ALL"));
        binding.chipOpen.setOnClickListener(v -> filterPosts("OPEN"));
        binding.chipInProgress.setOnClickListener(v -> filterPosts("IN_PROGRESS"));
        binding.chipCompleted.setOnClickListener(v -> filterPosts("COMPLETED"));
        binding.chipCancelled.setOnClickListener(v -> filterPosts("CANCELLED"));
        
        // Set default selection
        binding.chipAll.setChecked(true);
    }

    private void filterPosts(String status) {
        currentFilter = status;
        
        // Update chip selection
        binding.chipAll.setChecked("ALL".equals(status));
        binding.chipOpen.setChecked("OPEN".equals(status));
        binding.chipInProgress.setChecked("IN_PROGRESS".equals(status));
        binding.chipCompleted.setChecked("COMPLETED".equals(status));
        binding.chipCancelled.setChecked("CANCELLED".equals(status));
        
        loadPosts();
    }

    private void loadPosts() {
        binding.progressBar.setVisibility(View.VISIBLE);
        
        if ("ALL".equals(currentFilter)) {
            database.maintenancePostDao().getAllPosts().observe(getViewLifecycleOwner(), posts -> {
                updatePostsList(posts);
            });
        } else {
            database.maintenancePostDao().getPostsByStatus(currentFilter).observe(getViewLifecycleOwner(), posts -> {
                updatePostsList(posts);
            });
        }
    }

    private void updatePostsList(List<MaintenancePost> posts) {
        binding.progressBar.setVisibility(View.GONE);
        
        if (posts != null && !posts.isEmpty()) {
            binding.layoutEmptyState.setVisibility(View.GONE);
            binding.recyclerViewPosts.setVisibility(View.VISIBLE);
            postsAdapter.updatePosts(posts);
        } else {
            binding.layoutEmptyState.setVisibility(View.VISIBLE);
            binding.recyclerViewPosts.setVisibility(View.GONE);
            binding.tvEmptyState.setText("No posts found for the selected filter");
        }
    }

    @Override
    public void onPostClick(MaintenancePost post) {
        // Navigate to post details
        Toast.makeText(getContext(), "Post: " + post.getTitle(), Toast.LENGTH_SHORT).show();
    }

    @Override
    public void onPostStatusChange(MaintenancePost post, String newStatus) {
        database.maintenancePostDao().updatePostStatus(post.getId(), newStatus, System.currentTimeMillis());
        Toast.makeText(getContext(), "Post status updated to " + newStatus, Toast.LENGTH_SHORT).show();
    }

    @Override
    public void onPostDelete(MaintenancePost post) {
        // Show confirmation dialog and delete post
        Toast.makeText(getContext(), "Delete functionality will be implemented with confirmation dialog", 
                Toast.LENGTH_SHORT).show();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        binding = null;
    }
}
