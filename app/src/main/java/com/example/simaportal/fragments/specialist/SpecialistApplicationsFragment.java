package com.example.simaportal.fragments.specialist;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.example.simaportal.auth.AuthManager;
import com.example.simaportal.databinding.FragmentSpecialistApplicationsBinding;
import com.example.simaportal.database.SIMADatabase;
import com.example.simaportal.database.entities.Specialist;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class SpecialistApplicationsFragment extends Fragment {

    private FragmentSpecialistApplicationsBinding binding;
    private AuthManager authManager;
    private SIMADatabase database;
    private ExecutorService executorService;

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        binding = FragmentSpecialistApplicationsBinding.inflate(inflater, container, false);
        return binding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        
        authManager = AuthManager.getInstance(requireContext());
        database = SIMADatabase.getInstance(requireContext());
        executorService = Executors.newFixedThreadPool(2);
        
        loadApplications();
    }

    private void loadApplications() {
        int currentUserId = authManager.getCurrentUserId();
        if (currentUserId == -1) {
            Toast.makeText(getContext(), "Error: Unable to get current user", Toast.LENGTH_SHORT).show();
            return;
        }

        executorService.execute(() -> {
            try {
                Specialist specialist = database.specialistDao().getSpecialistByUserIdSync(currentUserId);
                if (specialist != null) {
                    requireActivity().runOnUiThread(() -> {
                        loadSpecialistApplications(specialist.getId());
                    });
                }
            } catch (Exception e) {
                requireActivity().runOnUiThread(() -> {
                    Toast.makeText(getContext(), "Error loading applications: " + e.getMessage(), 
                            Toast.LENGTH_SHORT).show();
                });
            }
        });
    }

    private void loadSpecialistApplications(int specialistId) {
        // This will be implemented when we create the applications adapter
        binding.tvEmptyState.setVisibility(View.VISIBLE);
        binding.tvEmptyState.setText("No applications yet. Apply to maintenance posts to see them here!");
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
        }
        binding = null;
    }
}
