package com.example.simaportal.fragments.specialist;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.example.simaportal.adapters.PostsAdapter;
import com.example.simaportal.auth.AuthManager;
import com.example.simaportal.databinding.FragmentSpecialistPostsBinding;
import com.example.simaportal.database.SIMADatabase;
import com.example.simaportal.database.entities.MaintenancePost;
import com.example.simaportal.database.entities.Specialist;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class SpecialistPostsFragment extends Fragment implements PostsAdapter.OnPostActionListener {

    private FragmentSpecialistPostsBinding binding;
    private AuthManager authManager;
    private SIMADatabase database;
    private PostsAdapter postsAdapter;
    private ExecutorService executorService;
    private Specialist currentSpecialist;

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        binding = FragmentSpecialistPostsBinding.inflate(inflater, container, false);
        return binding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        
        authManager = AuthManager.getInstance(requireContext());
        database = SIMADatabase.getInstance(requireContext());
        executorService = Executors.newFixedThreadPool(2);
        
        setupRecyclerView();
        loadSpecialistData();
    }

    private void setupRecyclerView() {
        postsAdapter = new PostsAdapter(new ArrayList<>(), this, false); // false for specialist mode
        binding.recyclerViewPosts.setLayoutManager(new LinearLayoutManager(getContext()));
        binding.recyclerViewPosts.setAdapter(postsAdapter);
    }

    private void loadSpecialistData() {
        int currentUserId = authManager.getCurrentUserId();
        if (currentUserId == -1) {
            Toast.makeText(getContext(), "Error: Unable to get current user", Toast.LENGTH_SHORT).show();
            return;
        }

        executorService.execute(() -> {
            try {
                currentSpecialist = database.specialistDao().getSpecialistByUserIdSync(currentUserId);
                if (currentSpecialist != null) {
                    requireActivity().runOnUiThread(() -> {
                        loadAvailablePosts();
                    });
                } else {
                    requireActivity().runOnUiThread(() -> {
                        Toast.makeText(getContext(), "Specialist profile not found", Toast.LENGTH_SHORT).show();
                    });
                }
            } catch (Exception e) {
                requireActivity().runOnUiThread(() -> {
                    Toast.makeText(getContext(), "Error loading specialist data: " + e.getMessage(), 
                            Toast.LENGTH_SHORT).show();
                });
            }
        });
    }

    private void loadAvailablePosts() {
        if (currentSpecialist == null) {
            return;
        }

        binding.progressBar.setVisibility(View.VISIBLE);
        
        // Load posts for specialist's category
        database.maintenancePostDao().getOpenPostsByCategory(currentSpecialist.getCategory())
                .observe(getViewLifecycleOwner(), posts -> {
                    updatePostsList(posts);
                });
    }

    private void updatePostsList(List<MaintenancePost> posts) {
        binding.progressBar.setVisibility(View.GONE);
        
        if (posts != null && !posts.isEmpty()) {
            binding.layoutEmptyState.setVisibility(View.GONE);
            binding.recyclerViewPosts.setVisibility(View.VISIBLE);
            postsAdapter.updatePosts(posts);
        } else {
            binding.layoutEmptyState.setVisibility(View.VISIBLE);
            binding.recyclerViewPosts.setVisibility(View.GONE);
            binding.tvEmptyState.setText("No available posts in your category (" + 
                    (currentSpecialist != null ? currentSpecialist.getCategory() : "Unknown") + ")");
        }
    }

    @Override
    public void onPostClick(MaintenancePost post) {
        // Navigate to post details or show application dialog
        showApplicationDialog(post);
    }

    @Override
    public void onPostStatusChange(MaintenancePost post, String newStatus) {
        // Not applicable for specialists
    }

    @Override
    public void onPostDelete(MaintenancePost post) {
        // Not applicable for specialists
    }

    private void showApplicationDialog(MaintenancePost post) {
        // Check if specialist has already applied
        if (currentSpecialist == null) {
            Toast.makeText(getContext(), "Error: Specialist data not available", Toast.LENGTH_SHORT).show();
            return;
        }

        executorService.execute(() -> {
            try {
                boolean hasApplied = database.applicationDao().hasSpecialistAppliedToPost(
                        post.getId(), currentSpecialist.getId());
                
                requireActivity().runOnUiThread(() -> {
                    if (hasApplied) {
                        Toast.makeText(getContext(), "You have already applied to this post", 
                                Toast.LENGTH_SHORT).show();
                    } else {
                        // Show application dialog or navigate to application activity
                        Toast.makeText(getContext(), "Application dialog will be implemented", 
                                Toast.LENGTH_SHORT).show();
                    }
                });
            } catch (Exception e) {
                requireActivity().runOnUiThread(() -> {
                    Toast.makeText(getContext(), "Error checking application status: " + e.getMessage(), 
                            Toast.LENGTH_SHORT).show();
                });
            }
        });
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
        }
        binding = null;
    }
}
