package com.example.simaportal.fragments.admin;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.example.simaportal.adapters.SpecialistsAdapter;
import com.example.simaportal.auth.AuthManager;
import com.example.simaportal.databinding.FragmentAdminSpecialistsBinding;
import com.example.simaportal.database.SIMADatabase;
import com.example.simaportal.database.entities.Specialist;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class AdminSpecialistsFragment extends Fragment implements SpecialistsAdapter.OnSpecialistActionListener {

    private FragmentAdminSpecialistsBinding binding;
    private SIMADatabase database;
    private AuthManager authManager;
    private SpecialistsAdapter specialistsAdapter;
    private ExecutorService executorService;
    private String currentFilter = "ALL";

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        binding = FragmentAdminSpecialistsBinding.inflate(inflater, container, false);
        return binding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        
        database = SIMADatabase.getInstance(requireContext());
        authManager = AuthManager.getInstance(requireContext());
        executorService = Executors.newFixedThreadPool(2);
        
        setupRecyclerView();
        setupFilters();
        loadSpecialists();
    }

    private void setupRecyclerView() {
        specialistsAdapter = new SpecialistsAdapter(new ArrayList<>(), this);
        binding.recyclerViewSpecialists.setLayoutManager(new LinearLayoutManager(getContext()));
        binding.recyclerViewSpecialists.setAdapter(specialistsAdapter);
    }

    private void setupFilters() {
        binding.chipAll.setOnClickListener(v -> filterSpecialists("ALL"));
        binding.chipPending.setOnClickListener(v -> filterSpecialists("PENDING"));
        binding.chipApproved.setOnClickListener(v -> filterSpecialists("APPROVED"));
        binding.chipSoftware.setOnClickListener(v -> filterSpecialists("SOFTWARE"));
        binding.chipNetwork.setOnClickListener(v -> filterSpecialists("NETWORK"));
        binding.chipHardware.setOnClickListener(v -> filterSpecialists("HARDWARE"));
        
        // Set default selection
        binding.chipAll.setChecked(true);
    }

    private void filterSpecialists(String filter) {
        currentFilter = filter;
        
        // Update chip selection
        binding.chipAll.setChecked("ALL".equals(filter));
        binding.chipPending.setChecked("PENDING".equals(filter));
        binding.chipApproved.setChecked("APPROVED".equals(filter));
        binding.chipSoftware.setChecked("SOFTWARE".equals(filter));
        binding.chipNetwork.setChecked("NETWORK".equals(filter));
        binding.chipHardware.setChecked("HARDWARE".equals(filter));
        
        loadSpecialists();
    }

    private void loadSpecialists() {
        binding.progressBar.setVisibility(View.VISIBLE);
        
        switch (currentFilter) {
            case "ALL":
                database.specialistDao().getAllSpecialists().observe(getViewLifecycleOwner(), specialists -> {
                    updateSpecialistsList(specialists);
                });
                break;
            case "PENDING":
                database.specialistDao().getPendingApprovalSpecialists().observe(getViewLifecycleOwner(), specialists -> {
                    updateSpecialistsList(specialists);
                });
                break;
            case "APPROVED":
                database.specialistDao().getAllApprovedSpecialists().observe(getViewLifecycleOwner(), specialists -> {
                    updateSpecialistsList(specialists);
                });
                break;
            case "SOFTWARE":
            case "NETWORK":
            case "HARDWARE":
                database.specialistDao().getSpecialistsByCategory(currentFilter).observe(getViewLifecycleOwner(), specialists -> {
                    updateSpecialistsList(specialists);
                });
                break;
        }
    }

    private void updateSpecialistsList(List<Specialist> specialists) {
        binding.progressBar.setVisibility(View.GONE);
        
        if (specialists != null && !specialists.isEmpty()) {
            binding.layoutEmptyState.setVisibility(View.GONE);
            binding.recyclerViewSpecialists.setVisibility(View.VISIBLE);
            specialistsAdapter.updateSpecialists(specialists);
        } else {
            binding.layoutEmptyState.setVisibility(View.VISIBLE);
            binding.recyclerViewSpecialists.setVisibility(View.GONE);
            binding.tvEmptyState.setText("No specialists found for the selected filter");
        }
    }

    @Override
    public void onSpecialistClick(Specialist specialist) {
        // Navigate to specialist details
        Toast.makeText(getContext(), "Specialist ID: " + specialist.getId(), Toast.LENGTH_SHORT).show();
    }

    @Override
    public void onApproveSpecialist(Specialist specialist) {
        int currentUserId = authManager.getCurrentUserId();
        if (currentUserId == -1) {
            Toast.makeText(getContext(), "Error: Unable to get current user", Toast.LENGTH_SHORT).show();
            return;
        }

        executorService.execute(() -> {
            try {
                long currentTime = System.currentTimeMillis();
                database.specialistDao().updateApprovalStatus(
                        specialist.getId(), 
                        true, 
                        currentUserId, 
                        currentTime, 
                        currentTime
                );
                
                requireActivity().runOnUiThread(() -> {
                    Toast.makeText(getContext(), "Specialist approved successfully", Toast.LENGTH_SHORT).show();
                });
            } catch (Exception e) {
                requireActivity().runOnUiThread(() -> {
                    Toast.makeText(getContext(), "Error approving specialist: " + e.getMessage(), 
                            Toast.LENGTH_SHORT).show();
                });
            }
        });
    }

    @Override
    public void onRejectSpecialist(Specialist specialist) {
        int currentUserId = authManager.getCurrentUserId();
        if (currentUserId == -1) {
            Toast.makeText(getContext(), "Error: Unable to get current user", Toast.LENGTH_SHORT).show();
            return;
        }

        // In a real app, you might want to show a dialog to get rejection reason
        executorService.execute(() -> {
            try {
                // For now, we'll just delete the specialist record
                // In a real app, you might want to keep the record with a rejected status
                database.specialistDao().delete(specialist);
                
                requireActivity().runOnUiThread(() -> {
                    Toast.makeText(getContext(), "Specialist application rejected", Toast.LENGTH_SHORT).show();
                });
            } catch (Exception e) {
                requireActivity().runOnUiThread(() -> {
                    Toast.makeText(getContext(), "Error rejecting specialist: " + e.getMessage(), 
                            Toast.LENGTH_SHORT).show();
                });
            }
        });
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
        }
        binding = null;
    }
}
