package com.example.simaportal.fragments.specialist;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.example.simaportal.auth.AuthManager;
import com.example.simaportal.databinding.FragmentSpecialistHomeBinding;
import com.example.simaportal.database.SIMADatabase;
import com.example.simaportal.database.entities.Specialist;
import com.example.simaportal.database.entities.User;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class SpecialistHomeFragment extends Fragment {

    private FragmentSpecialistHomeBinding binding;
    private AuthManager authManager;
    private SIMADatabase database;
    private ExecutorService executorService;

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        binding = FragmentSpecialistHomeBinding.inflate(inflater, container, false);
        return binding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        
        authManager = AuthManager.getInstance(requireContext());
        database = SIMADatabase.getInstance(requireContext());
        executorService = Executors.newFixedThreadPool(2);
        
        setupUI();
        observeUser();
    }

    private void setupUI() {
        binding.cardAvailablePosts.setOnClickListener(v -> {
            // Navigate to available posts
        });

        binding.cardMyApplications.setOnClickListener(v -> {
            // Navigate to my applications
        });

        binding.cardProfile.setOnClickListener(v -> {
            // Navigate to profile
        });
    }

    private void observeUser() {
        authManager.getCurrentUserLiveData().observe(getViewLifecycleOwner(), user -> {
            if (user != null) {
                updateUI(user);
                loadSpecialistData(user.getId());
            }
        });
    }

    private void updateUI(User user) {
        binding.tvWelcome.setText("Welcome, " + user.getFirstName() + "!");
        binding.tvSpecialistName.setText(user.getFullName());
        binding.tvSpecialistEmail.setText(user.getEmail());
    }

    private void loadSpecialistData(int userId) {
        executorService.execute(() -> {
            try {
                Specialist specialist = database.specialistDao().getSpecialistByUserIdSync(userId);
                if (specialist != null) {
                    requireActivity().runOnUiThread(() -> {
                        updateSpecialistUI(specialist);
                        loadDashboardStats(specialist);
                    });
                }
            } catch (Exception e) {
                // Handle error
            }
        });
    }

    private void updateSpecialistUI(Specialist specialist) {
        binding.tvCategory.setText(specialist.getCategory());
        binding.tvRating.setText(String.format("%.1f", specialist.getRating()));
        binding.tvJobsCompleted.setText(String.valueOf(specialist.getTotalJobsCompleted()));
        
        // Set category color
        switch (specialist.getCategory()) {
            case "SOFTWARE":
                binding.tvCategory.setTextColor(getResources().getColor(com.example.simaportal.R.color.software_color, null));
                break;
            case "NETWORK":
                binding.tvCategory.setTextColor(getResources().getColor(com.example.simaportal.R.color.network_color, null));
                break;
            case "HARDWARE":
                binding.tvCategory.setTextColor(getResources().getColor(com.example.simaportal.R.color.hardware_color, null));
                break;
        }

        // Set approval status
        if (specialist.isApproved()) {
            binding.chipApprovalStatus.setText("Approved");
            binding.chipApprovalStatus.setChipBackgroundColorResource(com.example.simaportal.R.color.success_color);
        } else {
            binding.chipApprovalStatus.setText("Pending Approval");
            binding.chipApprovalStatus.setChipBackgroundColorResource(com.example.simaportal.R.color.warning_color);
        }
    }

    private void loadDashboardStats(Specialist specialist) {
        // Load available posts count for specialist's category
        database.maintenancePostDao().getOpenPostCountByCategory(specialist.getCategory())
                .observe(getViewLifecycleOwner(), count -> {
                    if (count != null) {
                        binding.tvAvailablePosts.setText(String.valueOf(count));
                    }
                });

        // Load specialist's applications count
        database.applicationDao().getApplicationCountBySpecialist(specialist.getId())
                .observe(getViewLifecycleOwner(), count -> {
                    if (count != null) {
                        binding.tvTotalApplications.setText(String.valueOf(count));
                    }
                });

        // Load accepted applications count
        database.applicationDao().getAcceptedApplicationCountBySpecialist(specialist.getId())
                .observe(getViewLifecycleOwner(), count -> {
                    if (count != null) {
                        binding.tvAcceptedApplications.setText(String.valueOf(count));
                    }
                });
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
        }
        binding = null;
    }
}
