package com.example.simaportal.fragments.admin;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.example.simaportal.adapters.UsersAdapter;
import com.example.simaportal.databinding.FragmentAdminUsersBinding;
import com.example.simaportal.database.SIMADatabase;
import com.example.simaportal.database.entities.User;

import java.util.ArrayList;
import java.util.List;

public class AdminUsersFragment extends Fragment implements UsersAdapter.OnUserActionListener {

    private FragmentAdminUsersBinding binding;
    private SIMADatabase database;
    private UsersAdapter usersAdapter;
    private String currentFilter = "ALL";

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        binding = FragmentAdminUsersBinding.inflate(inflater, container, false);
        return binding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        
        database = SIMADatabase.getInstance(requireContext());
        
        setupRecyclerView();
        setupFilters();
        loadUsers();
    }

    private void setupRecyclerView() {
        usersAdapter = new UsersAdapter(new ArrayList<>(), this);
        binding.recyclerViewUsers.setLayoutManager(new LinearLayoutManager(getContext()));
        binding.recyclerViewUsers.setAdapter(usersAdapter);
    }

    private void setupFilters() {
        binding.chipAll.setOnClickListener(v -> filterUsers("ALL"));
        binding.chipUsers.setOnClickListener(v -> filterUsers("USER"));
        binding.chipSpecialists.setOnClickListener(v -> filterUsers("SPECIALIST"));
        binding.chipAdmins.setOnClickListener(v -> filterUsers("ADMIN"));
        
        // Set default selection
        binding.chipAll.setChecked(true);
    }

    private void filterUsers(String role) {
        currentFilter = role;
        
        // Update chip selection
        binding.chipAll.setChecked("ALL".equals(role));
        binding.chipUsers.setChecked("USER".equals(role));
        binding.chipSpecialists.setChecked("SPECIALIST".equals(role));
        binding.chipAdmins.setChecked("ADMIN".equals(role));
        
        loadUsers();
    }

    private void loadUsers() {
        binding.progressBar.setVisibility(View.VISIBLE);
        
        if ("ALL".equals(currentFilter)) {
            database.userDao().getAllActiveUsers().observe(getViewLifecycleOwner(), users -> {
                updateUsersList(users);
            });
        } else {
            database.userDao().getUsersByRole(currentFilter).observe(getViewLifecycleOwner(), users -> {
                updateUsersList(users);
            });
        }
    }

    private void updateUsersList(List<User> users) {
        binding.progressBar.setVisibility(View.GONE);
        
        if (users != null && !users.isEmpty()) {
            binding.layoutEmptyState.setVisibility(View.GONE);
            binding.recyclerViewUsers.setVisibility(View.VISIBLE);
            usersAdapter.updateUsers(users);
        } else {
            binding.layoutEmptyState.setVisibility(View.VISIBLE);
            binding.recyclerViewUsers.setVisibility(View.GONE);
            binding.tvEmptyState.setText("No users found for the selected filter");
        }
    }

    @Override
    public void onUserClick(User user) {
        // Navigate to user details or show user info dialog
        Toast.makeText(getContext(), "User: " + user.getFullName(), Toast.LENGTH_SHORT).show();
    }

    @Override
    public void onUserStatusToggle(User user) {
        // Toggle user active status
        database.userDao().updateUserStatus(user.getId(), !user.isActive(), System.currentTimeMillis());
        Toast.makeText(getContext(), 
                user.isActive() ? "User deactivated" : "User activated", 
                Toast.LENGTH_SHORT).show();
    }

    @Override
    public void onUserDelete(User user) {
        // Show confirmation dialog and delete user
        // This is a critical operation, should have proper confirmation
        Toast.makeText(getContext(), "Delete functionality will be implemented with confirmation dialog", 
                Toast.LENGTH_SHORT).show();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        binding = null;
    }
}
