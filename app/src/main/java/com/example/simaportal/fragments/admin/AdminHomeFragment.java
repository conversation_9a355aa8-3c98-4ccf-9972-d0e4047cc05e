package com.example.simaportal.fragments.admin;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.ViewModelProvider;

import com.example.simaportal.auth.AuthManager;
import com.example.simaportal.databinding.FragmentAdminHomeBinding;
import com.example.simaportal.database.entities.User;
import com.example.simaportal.database.SIMADatabase;

public class AdminHomeFragment extends Fragment {

    private FragmentAdminHomeBinding binding;
    private AuthManager authManager;
    private SIMADatabase database;

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        binding = FragmentAdminHomeBinding.inflate(inflater, container, false);
        return binding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        
        authManager = AuthManager.getInstance(requireContext());
        database = SIMADatabase.getInstance(requireContext());
        
        setupUI();
        observeUser();
        loadDashboardData();
    }

    private void setupUI() {
        binding.cardUsers.setOnClickListener(v -> {
            // Navigate to users management
        });

        binding.cardPosts.setOnClickListener(v -> {
            // Navigate to posts management
        });

        binding.cardSpecialists.setOnClickListener(v -> {
            // Navigate to specialists management
        });

        binding.cardApplications.setOnClickListener(v -> {
            // Navigate to applications management
        });
    }

    private void observeUser() {
        authManager.getCurrentUserLiveData().observe(getViewLifecycleOwner(), user -> {
            if (user != null) {
                updateUI(user);
            }
        });
    }

    private void updateUI(User user) {
        binding.tvWelcome.setText("Welcome, " + user.getFirstName() + "!");
        binding.tvAdminName.setText(user.getFullName());
        binding.tvAdminEmail.setText(user.getEmail());
    }

    private void loadDashboardData() {
        // Load total users count
        database.userDao().getTotalActiveUserCount().observe(getViewLifecycleOwner(), count -> {
            if (count != null) {
                binding.tvTotalUsers.setText(String.valueOf(count));
            }
        });

        // Load total posts count
        database.maintenancePostDao().getPostCountByStatus("OPEN").observe(getViewLifecycleOwner(), count -> {
            if (count != null) {
                binding.tvActivePosts.setText(String.valueOf(count));
            }
        });

        // Load total specialists count
        database.specialistDao().getTotalApprovedSpecialistCount().observe(getViewLifecycleOwner(), count -> {
            if (count != null) {
                binding.tvTotalSpecialists.setText(String.valueOf(count));
            }
        });

        // Load pending approvals count
        database.specialistDao().getPendingApprovalCount().observe(getViewLifecycleOwner(), count -> {
            if (count != null) {
                binding.tvPendingApprovals.setText(String.valueOf(count));
                if (count > 0) {
                    binding.chipPendingApprovals.setVisibility(View.VISIBLE);
                    binding.chipPendingApprovals.setText(count + " pending");
                } else {
                    binding.chipPendingApprovals.setVisibility(View.GONE);
                }
            }
        });

        // Load user counts by role
        database.userDao().getUserCountByRole("USER").observe(getViewLifecycleOwner(), count -> {
            if (count != null) {
                binding.tvRegularUsers.setText(String.valueOf(count));
            }
        });

        database.userDao().getUserCountByRole("SPECIALIST").observe(getViewLifecycleOwner(), count -> {
            if (count != null) {
                binding.tvSpecialistUsers.setText(String.valueOf(count));
            }
        });

        // Load category-wise specialist counts
        database.specialistDao().getSpecialistCountByCategory("SOFTWARE").observe(getViewLifecycleOwner(), count -> {
            if (count != null) {
                binding.tvSoftwareSpecialists.setText(String.valueOf(count));
            }
        });

        database.specialistDao().getSpecialistCountByCategory("NETWORK").observe(getViewLifecycleOwner(), count -> {
            if (count != null) {
                binding.tvNetworkSpecialists.setText(String.valueOf(count));
            }
        });

        database.specialistDao().getSpecialistCountByCategory("HARDWARE").observe(getViewLifecycleOwner(), count -> {
            if (count != null) {
                binding.tvHardwareSpecialists.setText(String.valueOf(count));
            }
        });
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        binding = null;
    }
}
