package com.example.simaportal.fragments.specialist;

import android.content.Intent;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.example.simaportal.activities.EditProfileActivity;
import com.example.simaportal.activities.specialist.SpecialistDashboardActivity;
import com.example.simaportal.auth.AuthManager;
import com.example.simaportal.databinding.FragmentSpecialistProfileBinding;
import com.example.simaportal.database.SIMADatabase;
import com.example.simaportal.database.entities.Specialist;
import com.example.simaportal.database.entities.User;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class SpecialistProfileFragment extends Fragment {

    private FragmentSpecialistProfileBinding binding;
    private AuthManager authManager;
    private SIMADatabase database;
    private ExecutorService executorService;

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        binding = FragmentSpecialistProfileBinding.inflate(inflater, container, false);
        return binding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        
        authManager = AuthManager.getInstance(requireContext());
        database = SIMADatabase.getInstance(requireContext());
        executorService = Executors.newFixedThreadPool(2);
        
        setupUI();
        observeUser();
    }

    private void setupUI() {
        binding.btnEditProfile.setOnClickListener(v -> {
            Intent intent = new Intent(getContext(), EditProfileActivity.class);
            startActivity(intent);
        });

        binding.btnLogout.setOnClickListener(v -> {
            if (getActivity() instanceof SpecialistDashboardActivity) {
                ((SpecialistDashboardActivity) getActivity()).logout();
            }
        });
    }

    private void observeUser() {
        authManager.getCurrentUserLiveData().observe(getViewLifecycleOwner(), user -> {
            if (user != null) {
                updateUI(user);
                loadSpecialistData(user.getId());
            }
        });
    }

    private void updateUI(User user) {
        binding.tvSpecialistName.setText(user.getFullName());
        binding.tvSpecialistEmail.setText(user.getEmail());
        binding.tvUserRole.setText(user.getRole());
        
        if (user.getPhoneNumber() != null && !user.getPhoneNumber().isEmpty()) {
            binding.tvSpecialistPhone.setText(user.getPhoneNumber());
            binding.layoutPhone.setVisibility(View.VISIBLE);
        } else {
            binding.layoutPhone.setVisibility(View.GONE);
        }
        
        if (user.getDateOfBirth() != null && !user.getDateOfBirth().isEmpty()) {
            binding.tvSpecialistDob.setText(user.getDateOfBirth());
            binding.layoutDob.setVisibility(View.VISIBLE);
        } else {
            binding.layoutDob.setVisibility(View.GONE);
        }
    }

    private void loadSpecialistData(int userId) {
        executorService.execute(() -> {
            try {
                Specialist specialist = database.specialistDao().getSpecialistByUserIdSync(userId);
                if (specialist != null) {
                    requireActivity().runOnUiThread(() -> {
                        updateSpecialistUI(specialist);
                    });
                }
            } catch (Exception e) {
                // Handle error
            }
        });
    }

    private void updateSpecialistUI(Specialist specialist) {
        binding.tvCategory.setText(specialist.getCategory());
        binding.tvExperience.setText(specialist.getExperienceYears() + " years");
        binding.tvRating.setText(String.format("%.1f", specialist.getRating()));
        binding.tvJobsCompleted.setText(String.valueOf(specialist.getTotalJobsCompleted()));
        
        // Set category color
        switch (specialist.getCategory()) {
            case "SOFTWARE":
                binding.tvCategory.setTextColor(getResources().getColor(com.example.simaportal.R.color.software_color, null));
                break;
            case "NETWORK":
                binding.tvCategory.setTextColor(getResources().getColor(com.example.simaportal.R.color.network_color, null));
                break;
            case "HARDWARE":
                binding.tvCategory.setTextColor(getResources().getColor(com.example.simaportal.R.color.hardware_color, null));
                break;
        }

        // Set approval status
        if (specialist.isApproved()) {
            binding.chipApprovalStatus.setText("Approved");
            binding.chipApprovalStatus.setChipBackgroundColorResource(com.example.simaportal.R.color.success_color);
        } else {
            binding.chipApprovalStatus.setText("Pending Approval");
            binding.chipApprovalStatus.setChipBackgroundColorResource(com.example.simaportal.R.color.warning_color);
        }

        // Set availability status
        binding.chipAvailabilityStatus.setText(specialist.getAvailabilityStatus());
        switch (specialist.getAvailabilityStatus()) {
            case "AVAILABLE":
                binding.chipAvailabilityStatus.setChipBackgroundColorResource(com.example.simaportal.R.color.success_color);
                break;
            case "BUSY":
                binding.chipAvailabilityStatus.setChipBackgroundColorResource(com.example.simaportal.R.color.warning_color);
                break;
            case "OFFLINE":
                binding.chipAvailabilityStatus.setChipBackgroundColorResource(com.example.simaportal.R.color.error_color);
                break;
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
        }
        binding = null;
    }
}
