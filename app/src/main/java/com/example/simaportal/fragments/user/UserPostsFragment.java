package com.example.simaportal.fragments.user;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.example.simaportal.auth.AuthManager;
import com.example.simaportal.databinding.FragmentUserPostsBinding;

public class UserPostsFragment extends Fragment {

    private FragmentUserPostsBinding binding;
    private AuthManager authManager;

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        binding = FragmentUserPostsBinding.inflate(inflater, container, false);
        return binding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        
        authManager = AuthManager.getInstance(requireContext());
        
        setupRecyclerView();
        loadUserPosts();
    }

    private void setupRecyclerView() {
        binding.recyclerViewPosts.setLayoutManager(new LinearLayoutManager(getContext()));
        // Adapter will be implemented later
    }

    private void loadUserPosts() {
        // This will be implemented when we create the post management system
        binding.tvEmptyState.setVisibility(View.VISIBLE);
        binding.tvEmptyState.setText("No posts yet. Create your first maintenance request!");
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        binding = null;
    }
}
