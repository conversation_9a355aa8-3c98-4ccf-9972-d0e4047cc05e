package com.example.simaportal.fragments.admin;

import android.content.Intent;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.example.simaportal.activities.admin.AdminDashboardActivity;
import com.example.simaportal.activities.EditProfileActivity;
import com.example.simaportal.auth.AuthManager;
import com.example.simaportal.databinding.FragmentAdminProfileBinding;
import com.example.simaportal.database.entities.User;

public class AdminProfileFragment extends Fragment {

    private FragmentAdminProfileBinding binding;
    private AuthManager authManager;

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        binding = FragmentAdminProfileBinding.inflate(inflater, container, false);
        return binding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        
        authManager = AuthManager.getInstance(requireContext());
        
        setupUI();
        observeUser();
    }

    private void setupUI() {
        binding.btnEditProfile.setOnClickListener(v -> {
            Intent intent = new Intent(getContext(), EditProfileActivity.class);
            startActivity(intent);
        });

        binding.btnSystemSettings.setOnClickListener(v -> {
            // Navigate to system settings
            // This would be implemented later
        });

        binding.btnLogout.setOnClickListener(v -> {
            if (getActivity() instanceof AdminDashboardActivity) {
                ((AdminDashboardActivity) getActivity()).logout();
            }
        });
    }

    private void observeUser() {
        authManager.getCurrentUserLiveData().observe(getViewLifecycleOwner(), user -> {
            if (user != null) {
                updateUI(user);
            }
        });
    }

    private void updateUI(User user) {
        binding.tvAdminName.setText(user.getFullName());
        binding.tvAdminEmail.setText(user.getEmail());
        binding.tvAdminRole.setText(user.getRole());
        
        if (user.getPhoneNumber() != null && !user.getPhoneNumber().isEmpty()) {
            binding.tvAdminPhone.setText(user.getPhoneNumber());
            binding.layoutPhone.setVisibility(View.VISIBLE);
        } else {
            binding.layoutPhone.setVisibility(View.GONE);
        }
        
        if (user.getDateOfBirth() != null && !user.getDateOfBirth().isEmpty()) {
            binding.tvAdminDob.setText(user.getDateOfBirth());
            binding.layoutDob.setVisibility(View.VISIBLE);
        } else {
            binding.layoutDob.setVisibility(View.GONE);
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        binding = null;
    }
}
