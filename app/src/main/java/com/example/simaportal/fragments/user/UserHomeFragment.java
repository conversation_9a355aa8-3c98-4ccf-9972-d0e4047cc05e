package com.example.simaportal.fragments.user;

import android.content.Intent;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.ViewModelProvider;

import com.example.simaportal.activities.user.CreatePostActivity;
import com.example.simaportal.auth.AuthManager;
import com.example.simaportal.databinding.FragmentUserHomeBinding;
import com.example.simaportal.database.entities.User;

public class UserHomeFragment extends Fragment {

    private FragmentUserHomeBinding binding;
    private AuthManager authManager;

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        binding = FragmentUserHomeBinding.inflate(inflater, container, false);
        return binding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        
        authManager = AuthManager.getInstance(requireContext());
        
        setupUI();
        observeUser();
    }

    private void setupUI() {
        binding.btnCreatePost.setOnClickListener(v -> {
            Intent intent = new Intent(getContext(), CreatePostActivity.class);
            startActivity(intent);
        });

        binding.btnViewAllPosts.setOnClickListener(v -> {
            // Navigate to posts fragment
            if (getActivity() != null) {
                // This will be implemented when we create the navigation logic
            }
        });
    }

    private void observeUser() {
        authManager.getCurrentUserLiveData().observe(getViewLifecycleOwner(), user -> {
            if (user != null) {
                updateUI(user);
            }
        });
    }

    private void updateUI(User user) {
        binding.tvWelcome.setText("Welcome, " + user.getFirstName() + "!");
        binding.tvUserName.setText(user.getFullName());
        binding.tvUserEmail.setText(user.getEmail());
        
        // Update stats - these will be implemented later with actual data
        binding.tvTotalPosts.setText("0");
        binding.tvActivePosts.setText("0");
        binding.tvCompletedPosts.setText("0");
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        binding = null;
    }
}
