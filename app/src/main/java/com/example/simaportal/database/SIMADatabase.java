package com.example.simaportal.database;

import android.content.Context;

import androidx.room.Database;
import androidx.room.Room;
import androidx.room.RoomDatabase;

import com.example.simaportal.database.dao.ApplicationDao;
import com.example.simaportal.database.dao.MaintenancePostDao;
import com.example.simaportal.database.dao.SpecialistDao;
import com.example.simaportal.database.dao.UserDao;
import com.example.simaportal.database.entities.Application;
import com.example.simaportal.database.entities.MaintenancePost;
import com.example.simaportal.database.entities.Specialist;
import com.example.simaportal.database.entities.User;

@Database(
        entities = {User.class, Specialist.class, MaintenancePost.class, Application.class},
        version = 1,
        exportSchema = false
)
public abstract class SIMADatabase extends RoomDatabase {

    private static final String DATABASE_NAME = "sima_portal_database";
    private static volatile SIMADatabase INSTANCE;

    // Abstract methods to get DAOs
    public abstract UserDao userDao();
    public abstract SpecialistDao specialistDao();
    public abstract MaintenancePostDao maintenancePostDao();
    public abstract ApplicationDao applicationDao();

    // Singleton pattern to get database instance
    public static SIMADatabase getInstance(Context context) {
        if (INSTANCE == null) {
            synchronized (SIMADatabase.class) {
                if (INSTANCE == null) {
                    INSTANCE = Room.databaseBuilder(
                            context.getApplicationContext(),
                            SIMADatabase.class,
                            DATABASE_NAME
                    )
                    .fallbackToDestructiveMigration() // For development only
                    .build();
                }
            }
        }
        return INSTANCE;
    }

    // Method to close database
    public static void destroyInstance() {
        if (INSTANCE != null && INSTANCE.isOpen()) {
            INSTANCE.close();
        }
        INSTANCE = null;
    }
}
