package com.example.simaportal.database.dao;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;

import com.example.simaportal.database.entities.MaintenancePost;

import java.util.List;

@Dao
public interface MaintenancePostDao {

    @Insert
    long insert(MaintenancePost post);

    @Update
    void update(MaintenancePost post);

    @Delete
    void delete(MaintenancePost post);

    @Query("SELECT * FROM maintenance_posts WHERE id = :id")
    LiveData<MaintenancePost> getPostById(int id);

    @Query("SELECT * FROM maintenance_posts WHERE id = :id")
    MaintenancePost getPostByIdSync(int id);

    @Query("SELECT * FROM maintenance_posts WHERE user_id = :userId ORDER BY created_at DESC")
    LiveData<List<MaintenancePost>> getPostsByUserId(int userId);

    @Query("SELECT * FROM maintenance_posts WHERE category = :category AND status = 'OPEN' ORDER BY created_at DESC")
    LiveData<List<MaintenancePost>> getOpenPostsByCategory(String category);

    @Query("SELECT * FROM maintenance_posts WHERE status = 'OPEN' ORDER BY created_at DESC")
    LiveData<List<MaintenancePost>> getAllOpenPosts();

    @Query("SELECT * FROM maintenance_posts WHERE status = :status ORDER BY created_at DESC")
    LiveData<List<MaintenancePost>> getPostsByStatus(String status);

    @Query("SELECT * FROM maintenance_posts WHERE assigned_specialist_id = :specialistId ORDER BY created_at DESC")
    LiveData<List<MaintenancePost>> getPostsBySpecialist(int specialistId);

    @Query("SELECT * FROM maintenance_posts ORDER BY created_at DESC")
    LiveData<List<MaintenancePost>> getAllPosts();

    @Query("SELECT * FROM maintenance_posts WHERE is_urgent = 1 AND status = 'OPEN' ORDER BY created_at DESC")
    LiveData<List<MaintenancePost>> getUrgentPosts();

    @Query("SELECT * FROM maintenance_posts WHERE priority = :priority AND status = 'OPEN' ORDER BY created_at DESC")
    LiveData<List<MaintenancePost>> getPostsByPriority(String priority);

    @Query("SELECT COUNT(*) FROM maintenance_posts WHERE status = :status")
    LiveData<Integer> getPostCountByStatus(String status);

    @Query("SELECT COUNT(*) FROM maintenance_posts WHERE category = :category AND status = 'OPEN'")
    LiveData<Integer> getOpenPostCountByCategory(String category);

    @Query("SELECT COUNT(*) FROM maintenance_posts WHERE user_id = :userId")
    LiveData<Integer> getPostCountByUser(int userId);

    @Query("UPDATE maintenance_posts SET views_count = views_count + 1, updated_at = :updatedAt WHERE id = :postId")
    void incrementViewsCount(int postId, long updatedAt);

    @Query("UPDATE maintenance_posts SET applications_count = applications_count + 1, updated_at = :updatedAt WHERE id = :postId")
    void incrementApplicationsCount(int postId, long updatedAt);

    @Query("UPDATE maintenance_posts SET status = :status, updated_at = :updatedAt WHERE id = :postId")
    void updatePostStatus(int postId, String status, long updatedAt);

    @Query("UPDATE maintenance_posts SET assigned_specialist_id = :specialistId, status = 'IN_PROGRESS', updated_at = :updatedAt WHERE id = :postId")
    void assignSpecialist(int postId, int specialistId, long updatedAt);

    @Query("UPDATE maintenance_posts SET status = 'COMPLETED', completed_at = :completedAt, updated_at = :updatedAt WHERE id = :postId")
    void markAsCompleted(int postId, long completedAt, long updatedAt);

    @Query("SELECT * FROM maintenance_posts WHERE (title LIKE :searchQuery OR description LIKE :searchQuery) AND status = 'OPEN'")
    LiveData<List<MaintenancePost>> searchOpenPosts(String searchQuery);

    @Query("SELECT * FROM maintenance_posts WHERE category = :category AND (title LIKE :searchQuery OR description LIKE :searchQuery) AND status = 'OPEN'")
    LiveData<List<MaintenancePost>> searchOpenPostsByCategory(String category, String searchQuery);

    @Query("SELECT * FROM maintenance_posts WHERE deadline BETWEEN :startDate AND :endDate AND status IN ('OPEN', 'IN_PROGRESS')")
    LiveData<List<MaintenancePost>> getPostsByDeadlineRange(long startDate, long endDate);

    @Query("SELECT * FROM maintenance_posts WHERE budget_min <= :maxBudget AND budget_max >= :minBudget AND status = 'OPEN'")
    LiveData<List<MaintenancePost>> getPostsByBudgetRange(double minBudget, double maxBudget);

    @Query("DELETE FROM maintenance_posts WHERE id = :postId")
    void deletePostById(int postId);

    @Query("SELECT * FROM maintenance_posts WHERE created_at >= :startTime ORDER BY created_at DESC")
    LiveData<List<MaintenancePost>> getRecentPosts(long startTime);

    @Query("SELECT AVG(CASE WHEN completed_at IS NOT NULL AND created_at IS NOT NULL THEN (completed_at - created_at) ELSE NULL END) FROM maintenance_posts WHERE status = 'COMPLETED'")
    LiveData<Long> getAverageCompletionTime();
}
