package com.example.simaportal.database.entities;

import androidx.room.Entity;
import androidx.room.PrimaryKey;
import androidx.room.ColumnInfo;
import androidx.room.ForeignKey;

@Entity(tableName = "maintenance_posts",
        foreignKeys = @ForeignKey(entity = User.class,
                parentColumns = "id",
                childColumns = "user_id",
                onDelete = ForeignKey.CASCADE))
public class MaintenancePost {
    @PrimaryKey(autoGenerate = true)
    private int id;

    @ColumnInfo(name = "user_id")
    private int userId;

    @ColumnInfo(name = "title")
    private String title;

    @ColumnInfo(name = "description")
    private String description;

    @ColumnInfo(name = "category")
    private String category; // "SOFTWARE", "NETWORK", "HARDWARE"

    @ColumnInfo(name = "priority")
    private String priority; // "LOW", "MEDIUM", "HIGH", "URGENT"

    @ColumnInfo(name = "location")
    private String location;

    @ColumnInfo(name = "budget_min")
    private Double budgetMin;

    @ColumnInfo(name = "budget_max")
    private Double budgetMax;

    @ColumnInfo(name = "deadline")
    private Long deadline;

    @ColumnInfo(name = "status")
    private String status; // "OPEN", "IN_PROGRESS", "COMPLETED", "CANCELLED"

    @ColumnInfo(name = "assigned_specialist_id")
    private Integer assignedSpecialistId;

    @ColumnInfo(name = "attachment_urls")
    private String attachmentUrls; // JSON string of file URLs

    @ColumnInfo(name = "requirements")
    private String requirements; // Specific requirements or skills needed

    @ColumnInfo(name = "estimated_duration")
    private String estimatedDuration; // e.g., "2 hours", "1 day", "1 week"

    @ColumnInfo(name = "contact_phone")
    private String contactPhone;

    @ColumnInfo(name = "contact_email")
    private String contactEmail;

    @ColumnInfo(name = "is_urgent")
    private boolean isUrgent;

    @ColumnInfo(name = "views_count")
    private int viewsCount;

    @ColumnInfo(name = "applications_count")
    private int applicationsCount;

    @ColumnInfo(name = "created_at")
    private long createdAt;

    @ColumnInfo(name = "updated_at")
    private long updatedAt;

    @ColumnInfo(name = "completed_at")
    private Long completedAt;

    // Constructors
    public MaintenancePost() {
        this.createdAt = System.currentTimeMillis();
        this.updatedAt = System.currentTimeMillis();
        this.status = "OPEN";
        this.viewsCount = 0;
        this.applicationsCount = 0;
        this.isUrgent = false;
    }

    public MaintenancePost(int userId, String title, String description, String category) {
        this();
        this.userId = userId;
        this.title = title;
        this.description = description;
        this.category = category;
    }

    // Getters and Setters
    public int getId() { return id; }
    public void setId(int id) { this.id = id; }

    public int getUserId() { return userId; }
    public void setUserId(int userId) { this.userId = userId; }

    public String getTitle() { return title; }
    public void setTitle(String title) { this.title = title; }

    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }

    public String getCategory() { return category; }
    public void setCategory(String category) { this.category = category; }

    public String getPriority() { return priority; }
    public void setPriority(String priority) { this.priority = priority; }

    public String getLocation() { return location; }
    public void setLocation(String location) { this.location = location; }

    public Double getBudgetMin() { return budgetMin; }
    public void setBudgetMin(Double budgetMin) { this.budgetMin = budgetMin; }

    public Double getBudgetMax() { return budgetMax; }
    public void setBudgetMax(Double budgetMax) { this.budgetMax = budgetMax; }

    public Long getDeadline() { return deadline; }
    public void setDeadline(Long deadline) { this.deadline = deadline; }

    public String getStatus() { return status; }
    public void setStatus(String status) { this.status = status; }

    public Integer getAssignedSpecialistId() { return assignedSpecialistId; }
    public void setAssignedSpecialistId(Integer assignedSpecialistId) { this.assignedSpecialistId = assignedSpecialistId; }

    public String getAttachmentUrls() { return attachmentUrls; }
    public void setAttachmentUrls(String attachmentUrls) { this.attachmentUrls = attachmentUrls; }

    public String getRequirements() { return requirements; }
    public void setRequirements(String requirements) { this.requirements = requirements; }

    public String getEstimatedDuration() { return estimatedDuration; }
    public void setEstimatedDuration(String estimatedDuration) { this.estimatedDuration = estimatedDuration; }

    public String getContactPhone() { return contactPhone; }
    public void setContactPhone(String contactPhone) { this.contactPhone = contactPhone; }

    public String getContactEmail() { return contactEmail; }
    public void setContactEmail(String contactEmail) { this.contactEmail = contactEmail; }

    public boolean isUrgent() { return isUrgent; }
    public void setUrgent(boolean urgent) { isUrgent = urgent; }

    public int getViewsCount() { return viewsCount; }
    public void setViewsCount(int viewsCount) { this.viewsCount = viewsCount; }

    public int getApplicationsCount() { return applicationsCount; }
    public void setApplicationsCount(int applicationsCount) { this.applicationsCount = applicationsCount; }

    public long getCreatedAt() { return createdAt; }
    public void setCreatedAt(long createdAt) { this.createdAt = createdAt; }

    public long getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(long updatedAt) { this.updatedAt = updatedAt; }

    public Long getCompletedAt() { return completedAt; }
    public void setCompletedAt(Long completedAt) { this.completedAt = completedAt; }

    // Helper methods
    public boolean isOpen() { return "OPEN".equals(status); }
    public boolean isInProgress() { return "IN_PROGRESS".equals(status); }
    public boolean isCompleted() { return "COMPLETED".equals(status); }
    public boolean isCancelled() { return "CANCELLED".equals(status); }

    public boolean isSoftwareCategory() { return "SOFTWARE".equals(category); }
    public boolean isNetworkCategory() { return "NETWORK".equals(category); }
    public boolean isHardwareCategory() { return "HARDWARE".equals(category); }

    public void incrementViewsCount() { this.viewsCount++; }
    public void incrementApplicationsCount() { this.applicationsCount++; }
}
