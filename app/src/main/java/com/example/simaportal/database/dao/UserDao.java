package com.example.simaportal.database.dao;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;

import com.example.simaportal.database.entities.User;

import java.util.List;

@Dao
public interface UserDao {

    @Insert
    long insert(User user);

    @Update
    void update(User user);

    @Delete
    void delete(User user);

    @Query("SELECT * FROM users WHERE id = :id")
    LiveData<User> getUserById(int id);

    @Query("SELECT * FROM users WHERE firebase_uid = :firebaseUid")
    LiveData<User> getUserByFirebaseUid(String firebaseUid);

    @Query("SELECT * FROM users WHERE firebase_uid = :firebaseUid")
    User getUserByFirebaseUidSync(String firebaseUid);

    @Query("SELECT * FROM users WHERE email = :email")
    LiveData<User> getUserByEmail(String email);

    @Query("SELECT * FROM users WHERE email = :email")
    User getUserByEmailSync(String email);

    @Query("SELECT * FROM users WHERE role = :role AND is_active = 1")
    LiveData<List<User>> getUsersByRole(String role);

    @Query("SELECT * FROM users WHERE is_active = 1 ORDER BY created_at DESC")
    LiveData<List<User>> getAllActiveUsers();

    @Query("SELECT * FROM users ORDER BY created_at DESC")
    LiveData<List<User>> getAllUsers();

    @Query("SELECT COUNT(*) FROM users WHERE role = :role AND is_active = 1")
    LiveData<Integer> getUserCountByRole(String role);

    @Query("SELECT COUNT(*) FROM users WHERE is_active = 1")
    LiveData<Integer> getTotalActiveUserCount();

    @Query("UPDATE users SET is_active = :isActive, updated_at = :updatedAt WHERE id = :userId")
    void updateUserStatus(int userId, boolean isActive, long updatedAt);

    @Query("UPDATE users SET email = :email, updated_at = :updatedAt WHERE id = :userId")
    void updateUserEmail(int userId, String email, long updatedAt);

    @Query("UPDATE users SET phone_number = :phoneNumber, updated_at = :updatedAt WHERE id = :userId")
    void updateUserPhone(int userId, String phoneNumber, long updatedAt);

    @Query("UPDATE users SET first_name = :firstName, last_name = :lastName, updated_at = :updatedAt WHERE id = :userId")
    void updateUserName(int userId, String firstName, String lastName, long updatedAt);

    @Query("UPDATE users SET date_of_birth = :dateOfBirth, updated_at = :updatedAt WHERE id = :userId")
    void updateUserDateOfBirth(int userId, String dateOfBirth, long updatedAt);

    @Query("UPDATE users SET profile_image_url = :profileImageUrl, updated_at = :updatedAt WHERE id = :userId")
    void updateUserProfileImage(int userId, String profileImageUrl, long updatedAt);

    @Query("SELECT * FROM users WHERE (first_name LIKE :searchQuery OR last_name LIKE :searchQuery OR email LIKE :searchQuery) AND is_active = 1")
    LiveData<List<User>> searchUsers(String searchQuery);

    @Query("SELECT * FROM users WHERE role = :role AND (first_name LIKE :searchQuery OR last_name LIKE :searchQuery OR email LIKE :searchQuery) AND is_active = 1")
    LiveData<List<User>> searchUsersByRole(String role, String searchQuery);

    @Query("DELETE FROM users WHERE id = :userId")
    void deleteUserById(int userId);

    @Query("SELECT EXISTS(SELECT 1 FROM users WHERE email = :email)")
    boolean isEmailExists(String email);

    @Query("SELECT EXISTS(SELECT 1 FROM users WHERE firebase_uid = :firebaseUid)")
    boolean isFirebaseUidExists(String firebaseUid);
}
