package com.example.simaportal.database.entities;

import androidx.room.Entity;
import androidx.room.PrimaryKey;
import androidx.room.ColumnInfo;
import androidx.room.ForeignKey;

@Entity(tableName = "specialists",
        foreignKeys = @ForeignKey(entity = User.class,
                parentColumns = "id",
                childColumns = "user_id",
                onDelete = ForeignKey.CASCADE))
public class Specialist {
    @PrimaryKey(autoGenerate = true)
    private int id;

    @ColumnInfo(name = "user_id")
    private int userId;

    @ColumnInfo(name = "category")
    private String category; // "SOFTWARE", "NETWORK", "HARDWARE"

    @ColumnInfo(name = "experience_years")
    private int experienceYears;

    @ColumnInfo(name = "skills")
    private String skills; // JSON string of skills array

    @ColumnInfo(name = "certifications")
    private String certifications; // JSON string of certifications

    @ColumnInfo(name = "cv_file_url")
    private String cvFileUrl;

    @ColumnInfo(name = "portfolio_url")
    private String portfolioUrl;

    @ColumnInfo(name = "hourly_rate")
    private double hourlyRate;

    @ColumnInfo(name = "availability_status")
    private String availabilityStatus; // "AVAILABLE", "BUSY", "OFFLINE"

    @ColumnInfo(name = "rating")
    private double rating;

    @ColumnInfo(name = "total_jobs_completed")
    private int totalJobsCompleted;

    @ColumnInfo(name = "is_approved")
    private boolean isApproved;

    @ColumnInfo(name = "approved_by")
    private Integer approvedBy; // Admin user ID

    @ColumnInfo(name = "approved_at")
    private Long approvedAt;

    @ColumnInfo(name = "created_at")
    private long createdAt;

    @ColumnInfo(name = "updated_at")
    private long updatedAt;

    // Constructors
    public Specialist() {
        this.createdAt = System.currentTimeMillis();
        this.updatedAt = System.currentTimeMillis();
        this.isApproved = false;
        this.rating = 0.0;
        this.totalJobsCompleted = 0;
        this.availabilityStatus = "AVAILABLE";
    }

    public Specialist(int userId, String category) {
        this();
        this.userId = userId;
        this.category = category;
    }

    // Getters and Setters
    public int getId() { return id; }
    public void setId(int id) { this.id = id; }

    public int getUserId() { return userId; }
    public void setUserId(int userId) { this.userId = userId; }

    public String getCategory() { return category; }
    public void setCategory(String category) { this.category = category; }

    public int getExperienceYears() { return experienceYears; }
    public void setExperienceYears(int experienceYears) { this.experienceYears = experienceYears; }

    public String getSkills() { return skills; }
    public void setSkills(String skills) { this.skills = skills; }

    public String getCertifications() { return certifications; }
    public void setCertifications(String certifications) { this.certifications = certifications; }

    public String getCvFileUrl() { return cvFileUrl; }
    public void setCvFileUrl(String cvFileUrl) { this.cvFileUrl = cvFileUrl; }

    public String getPortfolioUrl() { return portfolioUrl; }
    public void setPortfolioUrl(String portfolioUrl) { this.portfolioUrl = portfolioUrl; }

    public double getHourlyRate() { return hourlyRate; }
    public void setHourlyRate(double hourlyRate) { this.hourlyRate = hourlyRate; }

    public String getAvailabilityStatus() { return availabilityStatus; }
    public void setAvailabilityStatus(String availabilityStatus) { this.availabilityStatus = availabilityStatus; }

    public double getRating() { return rating; }
    public void setRating(double rating) { this.rating = rating; }

    public int getTotalJobsCompleted() { return totalJobsCompleted; }
    public void setTotalJobsCompleted(int totalJobsCompleted) { this.totalJobsCompleted = totalJobsCompleted; }

    public boolean isApproved() { return isApproved; }
    public void setApproved(boolean approved) { isApproved = approved; }

    public Integer getApprovedBy() { return approvedBy; }
    public void setApprovedBy(Integer approvedBy) { this.approvedBy = approvedBy; }

    public Long getApprovedAt() { return approvedAt; }
    public void setApprovedAt(Long approvedAt) { this.approvedAt = approvedAt; }

    public long getCreatedAt() { return createdAt; }
    public void setCreatedAt(long createdAt) { this.createdAt = createdAt; }

    public long getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(long updatedAt) { this.updatedAt = updatedAt; }

    // Helper methods
    public boolean isSoftwareSpecialist() {
        return "SOFTWARE".equals(category);
    }

    public boolean isNetworkSpecialist() {
        return "NETWORK".equals(category);
    }

    public boolean isHardwareSpecialist() {
        return "HARDWARE".equals(category);
    }

    public boolean isAvailable() {
        return "AVAILABLE".equals(availabilityStatus);
    }
}
