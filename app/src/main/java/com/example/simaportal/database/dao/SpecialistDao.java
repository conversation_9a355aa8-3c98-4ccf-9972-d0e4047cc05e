package com.example.simaportal.database.dao;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;

import com.example.simaportal.database.entities.Specialist;

import java.util.List;

@Dao
public interface SpecialistDao {

    @Insert
    long insert(Specialist specialist);

    @Update
    void update(Specialist specialist);

    @Delete
    void delete(Specialist specialist);

    @Query("SELECT * FROM specialists WHERE id = :id")
    LiveData<Specialist> getSpecialistById(int id);

    @Query("SELECT * FROM specialists WHERE user_id = :userId")
    LiveData<Specialist> getSpecialistByUserId(int userId);

    @Query("SELECT * FROM specialists WHERE user_id = :userId")
    Specialist getSpecialistByUserIdSync(int userId);

    @Query("SELECT * FROM specialists WHERE category = :category AND is_approved = 1")
    LiveData<List<Specialist>> getSpecialistsByCategory(String category);

    @Query("SELECT * FROM specialists WHERE is_approved = 1 AND availability_status = 'AVAILABLE'")
    LiveData<List<Specialist>> getAvailableSpecialists();

    @Query("SELECT * FROM specialists WHERE category = :category AND is_approved = 1 AND availability_status = 'AVAILABLE'")
    LiveData<List<Specialist>> getAvailableSpecialistsByCategory(String category);

    @Query("SELECT * FROM specialists WHERE is_approved = 0 ORDER BY created_at ASC")
    LiveData<List<Specialist>> getPendingApprovalSpecialists();

    @Query("SELECT * FROM specialists WHERE is_approved = 1 ORDER BY rating DESC, total_jobs_completed DESC")
    LiveData<List<Specialist>> getAllApprovedSpecialists();

    @Query("SELECT * FROM specialists ORDER BY created_at DESC")
    LiveData<List<Specialist>> getAllSpecialists();

    @Query("SELECT COUNT(*) FROM specialists WHERE category = :category AND is_approved = 1")
    LiveData<Integer> getSpecialistCountByCategory(String category);

    @Query("SELECT COUNT(*) FROM specialists WHERE is_approved = 1")
    LiveData<Integer> getTotalApprovedSpecialistCount();

    @Query("SELECT COUNT(*) FROM specialists WHERE is_approved = 0")
    LiveData<Integer> getPendingApprovalCount();

    @Query("UPDATE specialists SET is_approved = :isApproved, approved_by = :approvedBy, approved_at = :approvedAt, updated_at = :updatedAt WHERE id = :specialistId")
    void updateApprovalStatus(int specialistId, boolean isApproved, int approvedBy, long approvedAt, long updatedAt);

    @Query("UPDATE specialists SET availability_status = :status, updated_at = :updatedAt WHERE id = :specialistId")
    void updateAvailabilityStatus(int specialistId, String status, long updatedAt);

    @Query("UPDATE specialists SET rating = :rating, updated_at = :updatedAt WHERE id = :specialistId")
    void updateRating(int specialistId, double rating, long updatedAt);

    @Query("UPDATE specialists SET total_jobs_completed = total_jobs_completed + 1, updated_at = :updatedAt WHERE id = :specialistId")
    void incrementJobsCompleted(int specialistId, long updatedAt);

    @Query("UPDATE specialists SET hourly_rate = :hourlyRate, updated_at = :updatedAt WHERE id = :specialistId")
    void updateHourlyRate(int specialistId, double hourlyRate, long updatedAt);

    @Query("UPDATE specialists SET cv_file_url = :cvFileUrl, updated_at = :updatedAt WHERE id = :specialistId")
    void updateCvFile(int specialistId, String cvFileUrl, long updatedAt);

    @Query("UPDATE specialists SET skills = :skills, updated_at = :updatedAt WHERE id = :specialistId")
    void updateSkills(int specialistId, String skills, long updatedAt);

    @Query("UPDATE specialists SET certifications = :certifications, updated_at = :updatedAt WHERE id = :specialistId")
    void updateCertifications(int specialistId, String certifications, long updatedAt);

    @Query("SELECT * FROM specialists WHERE category = :category AND rating >= :minRating AND is_approved = 1 ORDER BY rating DESC")
    LiveData<List<Specialist>> getTopRatedSpecialistsByCategory(String category, double minRating);

    @Query("SELECT * FROM specialists WHERE is_approved = 1 AND (skills LIKE :searchQuery OR certifications LIKE :searchQuery)")
    LiveData<List<Specialist>> searchSpecialistsBySkills(String searchQuery);

    @Query("DELETE FROM specialists WHERE id = :specialistId")
    void deleteSpecialistById(int specialistId);

    @Query("SELECT EXISTS(SELECT 1 FROM specialists WHERE user_id = :userId)")
    boolean isUserAlreadySpecialist(int userId);
}
