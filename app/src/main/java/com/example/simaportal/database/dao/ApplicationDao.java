package com.example.simaportal.database.dao;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;

import com.example.simaportal.database.entities.Application;

import java.util.List;

@Dao
public interface ApplicationDao {

    @Insert
    long insert(Application application);

    @Update
    void update(Application application);

    @Delete
    void delete(Application application);

    @Query("SELECT * FROM applications WHERE id = :id")
    LiveData<Application> getApplicationById(int id);

    @Query("SELECT * FROM applications WHERE id = :id")
    Application getApplicationByIdSync(int id);

    @Query("SELECT * FROM applications WHERE post_id = :postId ORDER BY created_at DESC")
    LiveData<List<Application>> getApplicationsByPostId(int postId);

    @Query("SELECT * FROM applications WHERE specialist_id = :specialistId ORDER BY created_at DESC")
    LiveData<List<Application>> getApplicationsBySpecialistId(int specialistId);

    @Query("SELECT * FROM applications WHERE specialist_id = :specialistId AND status = :status ORDER BY created_at DESC")
    LiveData<List<Application>> getApplicationsBySpecialistAndStatus(int specialistId, String status);

    @Query("SELECT * FROM applications WHERE post_id = :postId AND status = :status ORDER BY created_at DESC")
    LiveData<List<Application>> getApplicationsByPostAndStatus(int postId, String status);

    @Query("SELECT * FROM applications WHERE status = 'PENDING' ORDER BY created_at ASC")
    LiveData<List<Application>> getPendingApplications();

    @Query("SELECT * FROM applications WHERE status = :status ORDER BY created_at DESC")
    LiveData<List<Application>> getApplicationsByStatus(String status);

    @Query("SELECT * FROM applications ORDER BY created_at DESC")
    LiveData<List<Application>> getAllApplications();

    @Query("SELECT COUNT(*) FROM applications WHERE post_id = :postId")
    LiveData<Integer> getApplicationCountByPost(int postId);

    @Query("SELECT COUNT(*) FROM applications WHERE specialist_id = :specialistId")
    LiveData<Integer> getApplicationCountBySpecialist(int specialistId);

    @Query("SELECT COUNT(*) FROM applications WHERE status = :status")
    LiveData<Integer> getApplicationCountByStatus(String status);

    @Query("SELECT COUNT(*) FROM applications WHERE specialist_id = :specialistId AND status = 'ACCEPTED'")
    LiveData<Integer> getAcceptedApplicationCountBySpecialist(int specialistId);

    @Query("UPDATE applications SET status = :status, reviewed_by = :reviewedBy, reviewed_at = :reviewedAt, updated_at = :updatedAt WHERE id = :applicationId")
    void updateApplicationStatus(int applicationId, String status, int reviewedBy, long reviewedAt, long updatedAt);

    @Query("UPDATE applications SET status = :status, reviewed_by = :reviewedBy, admin_notes = :adminNotes, reviewed_at = :reviewedAt, updated_at = :updatedAt WHERE id = :applicationId")
    void updateApplicationStatusWithNotes(int applicationId, String status, int reviewedBy, String adminNotes, long reviewedAt, long updatedAt);

    @Query("UPDATE applications SET admin_notes = :adminNotes, updated_at = :updatedAt WHERE id = :applicationId")
    void updateAdminNotes(int applicationId, String adminNotes, long updatedAt);

    @Query("SELECT * FROM applications WHERE post_id = :postId AND specialist_id = :specialistId")
    LiveData<Application> getApplicationByPostAndSpecialist(int postId, int specialistId);

    @Query("SELECT * FROM applications WHERE post_id = :postId AND specialist_id = :specialistId")
    Application getApplicationByPostAndSpecialistSync(int postId, int specialistId);

    @Query("SELECT EXISTS(SELECT 1 FROM applications WHERE post_id = :postId AND specialist_id = :specialistId)")
    boolean hasSpecialistAppliedToPost(int postId, int specialistId);

    @Query("SELECT * FROM applications WHERE specialist_id = :specialistId AND created_at >= :startTime ORDER BY created_at DESC")
    LiveData<List<Application>> getRecentApplicationsBySpecialist(int specialistId, long startTime);

    @Query("SELECT * FROM applications WHERE post_id = :postId AND created_at >= :startTime ORDER BY created_at DESC")
    LiveData<List<Application>> getRecentApplicationsByPost(int postId, long startTime);

    @Query("DELETE FROM applications WHERE id = :applicationId")
    void deleteApplicationById(int applicationId);

    @Query("DELETE FROM applications WHERE post_id = :postId")
    void deleteApplicationsByPostId(int postId);

    @Query("DELETE FROM applications WHERE specialist_id = :specialistId")
    void deleteApplicationsBySpecialistId(int specialistId);

    @Query("SELECT AVG(proposed_budget) FROM applications WHERE post_id = :postId AND status = 'PENDING'")
    LiveData<Double> getAverageProposedBudgetByPost(int postId);

    @Query("SELECT MIN(proposed_budget) FROM applications WHERE post_id = :postId AND status = 'PENDING'")
    LiveData<Double> getMinProposedBudgetByPost(int postId);

    @Query("SELECT MAX(proposed_budget) FROM applications WHERE post_id = :postId AND status = 'PENDING'")
    LiveData<Double> getMaxProposedBudgetByPost(int postId);
}
