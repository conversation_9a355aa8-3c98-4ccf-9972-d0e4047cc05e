package com.example.simaportal.database.entities;

import androidx.room.Entity;
import androidx.room.PrimaryKey;
import androidx.room.ColumnInfo;
import androidx.room.ForeignKey;

@Entity(tableName = "applications",
        foreignKeys = {
                @ForeignKey(entity = MaintenancePost.class,
                        parentColumns = "id",
                        childColumns = "post_id",
                        onDelete = ForeignKey.CASCADE),
                @ForeignKey(entity = Specialist.class,
                        parentColumns = "id",
                        childColumns = "specialist_id",
                        onDelete = ForeignKey.CASCADE)
        })
public class Application {
    @PrimaryKey(autoGenerate = true)
    private int id;

    @ColumnInfo(name = "post_id")
    private int postId;

    @ColumnInfo(name = "specialist_id")
    private int specialistId;

    @ColumnInfo(name = "cover_letter")
    private String coverLetter;

    @ColumnInfo(name = "proposed_budget")
    private Double proposedBudget;

    @ColumnInfo(name = "estimated_completion_time")
    private String estimatedCompletionTime;

    @ColumnInfo(name = "cv_file_url")
    private String cvFileUrl;

    @ColumnInfo(name = "portfolio_urls")
    private String portfolioUrls; // JSON string of portfolio URLs

    @ColumnInfo(name = "additional_documents")
    private String additionalDocuments; // JSON string of document URLs

    @ColumnInfo(name = "status")
    private String status; // "PENDING", "ACCEPTED", "REJECTED", "WITHDRAWN"

    @ColumnInfo(name = "application_notes")
    private String applicationNotes; // Additional notes from specialist

    @ColumnInfo(name = "admin_notes")
    private String adminNotes; // Notes from admin/user who reviews

    @ColumnInfo(name = "reviewed_by")
    private Integer reviewedBy; // User ID who reviewed the application

    @ColumnInfo(name = "reviewed_at")
    private Long reviewedAt;

    @ColumnInfo(name = "created_at")
    private long createdAt;

    @ColumnInfo(name = "updated_at")
    private long updatedAt;

    // Constructors
    public Application() {
        this.createdAt = System.currentTimeMillis();
        this.updatedAt = System.currentTimeMillis();
        this.status = "PENDING";
    }

    public Application(int postId, int specialistId, String coverLetter) {
        this();
        this.postId = postId;
        this.specialistId = specialistId;
        this.coverLetter = coverLetter;
    }

    // Getters and Setters
    public int getId() { return id; }
    public void setId(int id) { this.id = id; }

    public int getPostId() { return postId; }
    public void setPostId(int postId) { this.postId = postId; }

    public int getSpecialistId() { return specialistId; }
    public void setSpecialistId(int specialistId) { this.specialistId = specialistId; }

    public String getCoverLetter() { return coverLetter; }
    public void setCoverLetter(String coverLetter) { this.coverLetter = coverLetter; }

    public Double getProposedBudget() { return proposedBudget; }
    public void setProposedBudget(Double proposedBudget) { this.proposedBudget = proposedBudget; }

    public String getEstimatedCompletionTime() { return estimatedCompletionTime; }
    public void setEstimatedCompletionTime(String estimatedCompletionTime) { this.estimatedCompletionTime = estimatedCompletionTime; }

    public String getCvFileUrl() { return cvFileUrl; }
    public void setCvFileUrl(String cvFileUrl) { this.cvFileUrl = cvFileUrl; }

    public String getPortfolioUrls() { return portfolioUrls; }
    public void setPortfolioUrls(String portfolioUrls) { this.portfolioUrls = portfolioUrls; }

    public String getAdditionalDocuments() { return additionalDocuments; }
    public void setAdditionalDocuments(String additionalDocuments) { this.additionalDocuments = additionalDocuments; }

    public String getStatus() { return status; }
    public void setStatus(String status) { this.status = status; }

    public String getApplicationNotes() { return applicationNotes; }
    public void setApplicationNotes(String applicationNotes) { this.applicationNotes = applicationNotes; }

    public String getAdminNotes() { return adminNotes; }
    public void setAdminNotes(String adminNotes) { this.adminNotes = adminNotes; }

    public Integer getReviewedBy() { return reviewedBy; }
    public void setReviewedBy(Integer reviewedBy) { this.reviewedBy = reviewedBy; }

    public Long getReviewedAt() { return reviewedAt; }
    public void setReviewedAt(Long reviewedAt) { this.reviewedAt = reviewedAt; }

    public long getCreatedAt() { return createdAt; }
    public void setCreatedAt(long createdAt) { this.createdAt = createdAt; }

    public long getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(long updatedAt) { this.updatedAt = updatedAt; }

    // Helper methods
    public boolean isPending() { return "PENDING".equals(status); }
    public boolean isAccepted() { return "ACCEPTED".equals(status); }
    public boolean isRejected() { return "REJECTED".equals(status); }
    public boolean isWithdrawn() { return "WITHDRAWN".equals(status); }

    public void accept(int reviewerId) {
        this.status = "ACCEPTED";
        this.reviewedBy = reviewerId;
        this.reviewedAt = System.currentTimeMillis();
        this.updatedAt = System.currentTimeMillis();
    }

    public void reject(int reviewerId, String notes) {
        this.status = "REJECTED";
        this.reviewedBy = reviewerId;
        this.adminNotes = notes;
        this.reviewedAt = System.currentTimeMillis();
        this.updatedAt = System.currentTimeMillis();
    }

    public void withdraw() {
        this.status = "WITHDRAWN";
        this.updatedAt = System.currentTimeMillis();
    }
}
