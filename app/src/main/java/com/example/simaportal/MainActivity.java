package com.example.simaportal;

import android.content.Intent;
import android.os.Bundle;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

import com.example.simaportal.activities.LoginActivity;
import com.example.simaportal.activities.admin.AdminDashboardActivity;
import com.example.simaportal.activities.specialist.SpecialistDashboardActivity;
import com.example.simaportal.activities.user.UserDashboardActivity;
import com.example.simaportal.auth.AuthManager;
import com.example.simaportal.database.entities.User;

public class MainActivity extends AppCompatActivity {

    private AuthManager authManager;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        authManager = AuthManager.getInstance(this);

        // Check if user is logged in
        if (!authManager.isUserLoggedIn()) {
            navigateToLogin();
            return;
        }

        // Observe current user and navigate to appropriate dashboard
        authManager.getCurrentUserLiveData().observe(this, this::handleUserNavigation);
    }

    private void handleUserNavigation(User user) {
        if (user == null) {
            navigateToLogin();
            return;
        }

        Intent intent;
        switch (user.getRole()) {
            case "ADMIN":
                intent = new Intent(this, AdminDashboardActivity.class);
                break;
            case "SPECIALIST":
                intent = new Intent(this, SpecialistDashboardActivity.class);
                break;
            case "USER":
                intent = new Intent(this, UserDashboardActivity.class);
                break;
            default:
                Toast.makeText(this, "Unknown user role: " + user.getRole(), Toast.LENGTH_LONG).show();
                navigateToLogin();
                return;
        }

        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
        startActivity(intent);
        finish();
    }

    private void navigateToLogin() {
        Intent intent = new Intent(this, LoginActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
        startActivity(intent);
        finish();
    }
}