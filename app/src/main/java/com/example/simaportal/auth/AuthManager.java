package com.example.simaportal.auth;

import android.content.Context;
import android.content.SharedPreferences;

import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;

import com.example.simaportal.database.SIMADatabase;
import com.example.simaportal.database.entities.Specialist;
import com.example.simaportal.database.entities.User;
import com.google.firebase.auth.FirebaseAuth;
import com.google.firebase.auth.FirebaseUser;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class AuthManager {
    private static final String PREFS_NAME = "sima_auth_prefs";
    private static final String KEY_USER_ROLE = "user_role";
    private static final String KEY_USER_ID = "user_id";
    private static final String KEY_IS_LOGGED_IN = "is_logged_in";

    private static AuthManager instance;
    private FirebaseAuth firebaseAuth;
    private SIMADatabase database;
    private SharedPreferences sharedPreferences;
    private ExecutorService executorService;

    private MutableLiveData<User> currentUserLiveData = new MutableLiveData<>();
    private MutableLiveData<Boolean> isLoggedInLiveData = new MutableLiveData<>();
    private MutableLiveData<String> authErrorLiveData = new MutableLiveData<>();

    private AuthManager(Context context) {
        firebaseAuth = FirebaseAuth.getInstance();
        database = SIMADatabase.getInstance(context);
        sharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        executorService = Executors.newFixedThreadPool(2);

        // Initialize login state
        isLoggedInLiveData.setValue(isUserLoggedIn());

        // Listen to Firebase auth state changes
        firebaseAuth.addAuthStateListener(auth -> {
            FirebaseUser firebaseUser = auth.getCurrentUser();
            if (firebaseUser != null && isUserLoggedIn()) {
                loadCurrentUser(firebaseUser.getUid());
            } else {
                currentUserLiveData.setValue(null);
                isLoggedInLiveData.setValue(false);
                clearUserSession();
            }
        });
    }

    public static synchronized AuthManager getInstance(Context context) {
        if (instance == null) {
            instance = new AuthManager(context.getApplicationContext());
        }
        return instance;
    }

    // LiveData getters
    public LiveData<User> getCurrentUserLiveData() {
        return currentUserLiveData;
    }

    public LiveData<Boolean> getIsLoggedInLiveData() {
        return isLoggedInLiveData;
    }

    public LiveData<String> getAuthErrorLiveData() {
        return authErrorLiveData;
    }

    // Authentication methods
    public void signUp(String email, String password, String firstName, String lastName, 
                      String role, String specialistCategory, AuthCallback callback) {
        firebaseAuth.createUserWithEmailAndPassword(email, password)
                .addOnCompleteListener(task -> {
                    if (task.isSuccessful()) {
                        FirebaseUser firebaseUser = firebaseAuth.getCurrentUser();
                        if (firebaseUser != null) {
                            createUserInDatabase(firebaseUser.getUid(), email, firstName, 
                                               lastName, role, specialistCategory, callback);
                        }
                    } else {
                        String error = task.getException() != null ? 
                                      task.getException().getMessage() : "Sign up failed";
                        authErrorLiveData.setValue(error);
                        callback.onFailure(error);
                    }
                });
    }

    public void signIn(String email, String password, AuthCallback callback) {
        firebaseAuth.signInWithEmailAndPassword(email, password)
                .addOnCompleteListener(task -> {
                    if (task.isSuccessful()) {
                        FirebaseUser firebaseUser = firebaseAuth.getCurrentUser();
                        if (firebaseUser != null) {
                            loadUserAndSetSession(firebaseUser.getUid(), callback);
                        }
                    } else {
                        String error = task.getException() != null ? 
                                      task.getException().getMessage() : "Sign in failed";
                        authErrorLiveData.setValue(error);
                        callback.onFailure(error);
                    }
                });
    }

    public void signOut() {
        firebaseAuth.signOut();
        clearUserSession();
        currentUserLiveData.setValue(null);
        isLoggedInLiveData.setValue(false);
    }

    // User session management
    private void saveUserSession(User user) {
        SharedPreferences.Editor editor = sharedPreferences.edit();
        editor.putString(KEY_USER_ROLE, user.getRole());
        editor.putInt(KEY_USER_ID, user.getId());
        editor.putBoolean(KEY_IS_LOGGED_IN, true);
        editor.apply();
    }

    private void clearUserSession() {
        SharedPreferences.Editor editor = sharedPreferences.edit();
        editor.clear();
        editor.apply();
    }

    public boolean isUserLoggedIn() {
        return sharedPreferences.getBoolean(KEY_IS_LOGGED_IN, false) && 
               firebaseAuth.getCurrentUser() != null;
    }

    public String getCurrentUserRole() {
        return sharedPreferences.getString(KEY_USER_ROLE, "");
    }

    public int getCurrentUserId() {
        return sharedPreferences.getInt(KEY_USER_ID, -1);
    }

    // Database operations
    private void createUserInDatabase(String firebaseUid, String email, String firstName, 
                                    String lastName, String role, String specialistCategory, 
                                    AuthCallback callback) {
        executorService.execute(() -> {
            try {
                User user = new User(firebaseUid, email, firstName, lastName, role);
                long userId = database.userDao().insert(user);
                user.setId((int) userId);

                // If user is a specialist, create specialist record
                if ("SPECIALIST".equals(role) && specialistCategory != null) {
                    Specialist specialist = new Specialist((int) userId, specialistCategory);
                    database.specialistDao().insert(specialist);
                }

                saveUserSession(user);
                currentUserLiveData.postValue(user);
                isLoggedInLiveData.postValue(true);
                callback.onSuccess(user);

            } catch (Exception e) {
                String error = "Failed to create user: " + e.getMessage();
                authErrorLiveData.postValue(error);
                callback.onFailure(error);
            }
        });
    }

    private void loadUserAndSetSession(String firebaseUid, AuthCallback callback) {
        executorService.execute(() -> {
            try {
                User user = database.userDao().getUserByFirebaseUidSync(firebaseUid);
                if (user != null) {
                    saveUserSession(user);
                    currentUserLiveData.postValue(user);
                    isLoggedInLiveData.postValue(true);
                    callback.onSuccess(user);
                } else {
                    String error = "User not found in database";
                    authErrorLiveData.postValue(error);
                    callback.onFailure(error);
                }
            } catch (Exception e) {
                String error = "Failed to load user: " + e.getMessage();
                authErrorLiveData.postValue(error);
                callback.onFailure(error);
            }
        });
    }

    private void loadCurrentUser(String firebaseUid) {
        executorService.execute(() -> {
            try {
                User user = database.userDao().getUserByFirebaseUidSync(firebaseUid);
                currentUserLiveData.postValue(user);
            } catch (Exception e) {
                authErrorLiveData.postValue("Failed to load current user: " + e.getMessage());
            }
        });
    }

    // Callback interface
    public interface AuthCallback {
        void onSuccess(User user);
        void onFailure(String error);
    }
}
