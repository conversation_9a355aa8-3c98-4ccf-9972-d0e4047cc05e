package com.example.simaportal.adapters;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.example.simaportal.R;
import com.example.simaportal.database.entities.User;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.card.MaterialCardView;
import com.google.android.material.chip.Chip;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Locale;

public class UsersAdapter extends RecyclerView.Adapter<UsersAdapter.UserViewHolder> {

    private List<User> users;
    private OnUserActionListener listener;
    private SimpleDateFormat dateFormat = new SimpleDateFormat("MMM dd, yyyy", Locale.getDefault());

    public interface OnUserActionListener {
        void onUserClick(User user);
        void onUserStatusToggle(User user);
        void onUserDelete(User user);
    }

    public UsersAdapter(List<User> users, OnUserActionListener listener) {
        this.users = users;
        this.listener = listener;
    }

    @NonNull
    @Override
    public UserViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_user, parent, false);
        return new UserViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull UserViewHolder holder, int position) {
        User user = users.get(position);
        holder.bind(user);
    }

    @Override
    public int getItemCount() {
        return users.size();
    }

    public void updateUsers(List<User> newUsers) {
        this.users = newUsers;
        notifyDataSetChanged();
    }

    class UserViewHolder extends RecyclerView.ViewHolder {
        private MaterialCardView cardView;
        private TextView tvUserName;
        private TextView tvUserEmail;
        private TextView tvUserRole;
        private TextView tvCreatedDate;
        private Chip chipStatus;
        private MaterialButton btnToggleStatus;
        private MaterialButton btnDelete;

        public UserViewHolder(@NonNull View itemView) {
            super(itemView);
            cardView = itemView.findViewById(R.id.card_user);
            tvUserName = itemView.findViewById(R.id.tv_user_name);
            tvUserEmail = itemView.findViewById(R.id.tv_user_email);
            tvUserRole = itemView.findViewById(R.id.tv_user_role);
            tvCreatedDate = itemView.findViewById(R.id.tv_created_date);
            chipStatus = itemView.findViewById(R.id.chip_status);
            btnToggleStatus = itemView.findViewById(R.id.btn_toggle_status);
            btnDelete = itemView.findViewById(R.id.btn_delete);
        }

        public void bind(User user) {
            tvUserName.setText(user.getFullName());
            tvUserEmail.setText(user.getEmail());
            tvUserRole.setText(user.getRole());
            tvCreatedDate.setText("Joined: " + dateFormat.format(new Date(user.getCreatedAt())));

            // Set status chip
            if (user.isActive()) {
                chipStatus.setText("Active");
                chipStatus.setChipBackgroundColorResource(R.color.success_color);
                btnToggleStatus.setText("Deactivate");
            } else {
                chipStatus.setText("Inactive");
                chipStatus.setChipBackgroundColorResource(R.color.error_color);
                btnToggleStatus.setText("Activate");
            }

            // Set click listeners
            cardView.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onUserClick(user);
                }
            });

            btnToggleStatus.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onUserStatusToggle(user);
                }
            });

            btnDelete.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onUserDelete(user);
                }
            });
        }
    }
}
