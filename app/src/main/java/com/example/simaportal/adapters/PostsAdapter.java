package com.example.simaportal.adapters;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.example.simaportal.R;
import com.example.simaportal.database.entities.MaintenancePost;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.card.MaterialCardView;
import com.google.android.material.chip.Chip;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Locale;

public class PostsAdapter extends RecyclerView.Adapter<PostsAdapter.PostViewHolder> {

    private List<MaintenancePost> posts;
    private OnPostActionListener listener;
    private boolean isAdminMode;
    private SimpleDateFormat dateFormat = new SimpleDateFormat("MMM dd, yyyy", Locale.getDefault());

    public interface OnPostActionListener {
        void onPostClick(MaintenancePost post);
        void onPostStatusChange(MaintenancePost post, String newStatus);
        void onPostDelete(MaintenancePost post);
    }

    public PostsAdapter(List<MaintenancePost> posts, OnPostActionListener listener, boolean isAdminMode) {
        this.posts = posts;
        this.listener = listener;
        this.isAdminMode = isAdminMode;
    }

    @NonNull
    @Override
    public PostViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_post, parent, false);
        return new PostViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull PostViewHolder holder, int position) {
        MaintenancePost post = posts.get(position);
        holder.bind(post);
    }

    @Override
    public int getItemCount() {
        return posts.size();
    }

    public void updatePosts(List<MaintenancePost> newPosts) {
        this.posts = newPosts;
        notifyDataSetChanged();
    }

    class PostViewHolder extends RecyclerView.ViewHolder {
        private MaterialCardView cardView;
        private TextView tvTitle;
        private TextView tvDescription;
        private TextView tvCategory;
        private TextView tvPriority;
        private TextView tvBudget;
        private TextView tvLocation;
        private TextView tvCreatedDate;
        private TextView tvViewsCount;
        private TextView tvApplicationsCount;
        private Chip chipStatus;
        private Chip chipUrgent;
        private MaterialButton btnChangeStatus;
        private MaterialButton btnDelete;

        public PostViewHolder(@NonNull View itemView) {
            super(itemView);
            cardView = itemView.findViewById(R.id.card_post);
            tvTitle = itemView.findViewById(R.id.tv_title);
            tvDescription = itemView.findViewById(R.id.tv_description);
            tvCategory = itemView.findViewById(R.id.tv_category);
            tvPriority = itemView.findViewById(R.id.tv_priority);
            tvBudget = itemView.findViewById(R.id.tv_budget);
            tvLocation = itemView.findViewById(R.id.tv_location);
            tvCreatedDate = itemView.findViewById(R.id.tv_created_date);
            tvViewsCount = itemView.findViewById(R.id.tv_views_count);
            tvApplicationsCount = itemView.findViewById(R.id.tv_applications_count);
            chipStatus = itemView.findViewById(R.id.chip_status);
            chipUrgent = itemView.findViewById(R.id.chip_urgent);
            btnChangeStatus = itemView.findViewById(R.id.btn_change_status);
            btnDelete = itemView.findViewById(R.id.btn_delete);
        }

        public void bind(MaintenancePost post) {
            tvTitle.setText(post.getTitle());
            tvDescription.setText(post.getDescription());
            tvCategory.setText(post.getCategory());
            tvPriority.setText("Priority: " + post.getPriority());
            tvCreatedDate.setText("Posted: " + dateFormat.format(new Date(post.getCreatedAt())));
            tvViewsCount.setText(post.getViewsCount() + " views");
            tvApplicationsCount.setText(post.getApplicationsCount() + " applications");

            // Set budget
            if (post.getBudgetMin() != null && post.getBudgetMax() != null) {
                tvBudget.setText(String.format(Locale.getDefault(), 
                    "Budget: $%.2f - $%.2f", post.getBudgetMin(), post.getBudgetMax()));
                tvBudget.setVisibility(View.VISIBLE);
            } else {
                tvBudget.setVisibility(View.GONE);
            }

            // Set location
            if (post.getLocation() != null && !post.getLocation().isEmpty()) {
                tvLocation.setText("Location: " + post.getLocation());
                tvLocation.setVisibility(View.VISIBLE);
            } else {
                tvLocation.setVisibility(View.GONE);
            }

            // Set status chip
            chipStatus.setText(post.getStatus());
            switch (post.getStatus()) {
                case "OPEN":
                    chipStatus.setChipBackgroundColorResource(R.color.success_color);
                    break;
                case "IN_PROGRESS":
                    chipStatus.setChipBackgroundColorResource(R.color.warning_color);
                    break;
                case "COMPLETED":
                    chipStatus.setChipBackgroundColorResource(R.color.info_color);
                    break;
                case "CANCELLED":
                    chipStatus.setChipBackgroundColorResource(R.color.error_color);
                    break;
            }

            // Set urgent chip
            if (post.isUrgent()) {
                chipUrgent.setVisibility(View.VISIBLE);
                chipUrgent.setText("URGENT");
                chipUrgent.setChipBackgroundColorResource(R.color.priority_urgent);
            } else {
                chipUrgent.setVisibility(View.GONE);
            }

            // Set category color
            switch (post.getCategory()) {
                case "SOFTWARE":
                    tvCategory.setTextColor(itemView.getContext().getColor(R.color.software_color));
                    break;
                case "NETWORK":
                    tvCategory.setTextColor(itemView.getContext().getColor(R.color.network_color));
                    break;
                case "HARDWARE":
                    tvCategory.setTextColor(itemView.getContext().getColor(R.color.hardware_color));
                    break;
            }

            // Show admin controls if in admin mode
            if (isAdminMode) {
                btnChangeStatus.setVisibility(View.VISIBLE);
                btnDelete.setVisibility(View.VISIBLE);
            } else {
                btnChangeStatus.setVisibility(View.GONE);
                btnDelete.setVisibility(View.GONE);
            }

            // Set click listeners
            cardView.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onPostClick(post);
                }
            });

            btnChangeStatus.setOnClickListener(v -> {
                if (listener != null) {
                    // Cycle through statuses
                    String newStatus;
                    switch (post.getStatus()) {
                        case "OPEN":
                            newStatus = "IN_PROGRESS";
                            break;
                        case "IN_PROGRESS":
                            newStatus = "COMPLETED";
                            break;
                        case "COMPLETED":
                            newStatus = "CANCELLED";
                            break;
                        default:
                            newStatus = "OPEN";
                            break;
                    }
                    listener.onPostStatusChange(post, newStatus);
                }
            });

            btnDelete.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onPostDelete(post);
                }
            });
        }
    }
}
