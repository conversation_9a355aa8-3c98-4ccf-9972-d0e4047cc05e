package com.example.simaportal.adapters;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.example.simaportal.R;
import com.example.simaportal.database.entities.Specialist;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.card.MaterialCardView;
import com.google.android.material.chip.Chip;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Locale;

public class SpecialistsAdapter extends RecyclerView.Adapter<SpecialistsAdapter.SpecialistViewHolder> {

    private List<Specialist> specialists;
    private OnSpecialistActionListener listener;
    private SimpleDateFormat dateFormat = new SimpleDateFormat("MMM dd, yyyy", Locale.getDefault());

    public interface OnSpecialistActionListener {
        void onSpecialistClick(Specialist specialist);
        void onApproveSpecialist(Specialist specialist);
        void onRejectSpecialist(Specialist specialist);
    }

    public SpecialistsAdapter(List<Specialist> specialists, OnSpecialistActionListener listener) {
        this.specialists = specialists;
        this.listener = listener;
    }

    @NonNull
    @Override
    public SpecialistViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_specialist, parent, false);
        return new SpecialistViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull SpecialistViewHolder holder, int position) {
        Specialist specialist = specialists.get(position);
        holder.bind(specialist);
    }

    @Override
    public int getItemCount() {
        return specialists.size();
    }

    public void updateSpecialists(List<Specialist> newSpecialists) {
        this.specialists = newSpecialists;
        notifyDataSetChanged();
    }

    class SpecialistViewHolder extends RecyclerView.ViewHolder {
        private MaterialCardView cardView;
        private TextView tvSpecialistId;
        private TextView tvCategory;
        private TextView tvExperience;
        private TextView tvRating;
        private TextView tvJobsCompleted;
        private TextView tvCreatedDate;
        private Chip chipStatus;
        private Chip chipAvailability;
        private MaterialButton btnApprove;
        private MaterialButton btnReject;

        public SpecialistViewHolder(@NonNull View itemView) {
            super(itemView);
            cardView = itemView.findViewById(R.id.card_specialist);
            tvSpecialistId = itemView.findViewById(R.id.tv_specialist_id);
            tvCategory = itemView.findViewById(R.id.tv_category);
            tvExperience = itemView.findViewById(R.id.tv_experience);
            tvRating = itemView.findViewById(R.id.tv_rating);
            tvJobsCompleted = itemView.findViewById(R.id.tv_jobs_completed);
            tvCreatedDate = itemView.findViewById(R.id.tv_created_date);
            chipStatus = itemView.findViewById(R.id.chip_status);
            chipAvailability = itemView.findViewById(R.id.chip_availability);
            btnApprove = itemView.findViewById(R.id.btn_approve);
            btnReject = itemView.findViewById(R.id.btn_reject);
        }

        public void bind(Specialist specialist) {
            tvSpecialistId.setText("ID: " + specialist.getId());
            tvCategory.setText(specialist.getCategory());
            tvExperience.setText(specialist.getExperienceYears() + " years experience");
            tvRating.setText("Rating: " + String.format(Locale.getDefault(), "%.1f", specialist.getRating()));
            tvJobsCompleted.setText(specialist.getTotalJobsCompleted() + " jobs completed");
            tvCreatedDate.setText("Applied: " + dateFormat.format(new Date(specialist.getCreatedAt())));

            // Set status chip
            if (specialist.isApproved()) {
                chipStatus.setText("Approved");
                chipStatus.setChipBackgroundColorResource(R.color.success_color);
                btnApprove.setVisibility(View.GONE);
                btnReject.setVisibility(View.GONE);
            } else {
                chipStatus.setText("Pending");
                chipStatus.setChipBackgroundColorResource(R.color.warning_color);
                btnApprove.setVisibility(View.VISIBLE);
                btnReject.setVisibility(View.VISIBLE);
            }

            // Set availability chip
            chipAvailability.setText(specialist.getAvailabilityStatus());
            switch (specialist.getAvailabilityStatus()) {
                case "AVAILABLE":
                    chipAvailability.setChipBackgroundColorResource(R.color.success_color);
                    break;
                case "BUSY":
                    chipAvailability.setChipBackgroundColorResource(R.color.warning_color);
                    break;
                case "OFFLINE":
                    chipAvailability.setChipBackgroundColorResource(R.color.error_color);
                    break;
            }

            // Set category color
            switch (specialist.getCategory()) {
                case "SOFTWARE":
                    tvCategory.setTextColor(itemView.getContext().getColor(R.color.software_color));
                    break;
                case "NETWORK":
                    tvCategory.setTextColor(itemView.getContext().getColor(R.color.network_color));
                    break;
                case "HARDWARE":
                    tvCategory.setTextColor(itemView.getContext().getColor(R.color.hardware_color));
                    break;
            }

            // Set click listeners
            cardView.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onSpecialistClick(specialist);
                }
            });

            btnApprove.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onApproveSpecialist(specialist);
                }
            });

            btnReject.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onRejectSpecialist(specialist);
                }
            });
        }
    }
}
