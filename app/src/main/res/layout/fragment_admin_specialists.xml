<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/background_color">

    <!-- Header -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Specialist Management"
        android:textSize="20sp"
        android:textStyle="bold"
        android:textColor="@color/text_primary"
        android:padding="16dp" />

    <!-- Filter Chips -->
    <HorizontalScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:scrollbars="none">

        <com.google.android.material.chip.ChipGroup
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingHorizontal="16dp"
            android:paddingBottom="8dp"
            app:singleSelection="true">

            <com.google.android.material.chip.Chip
                android:id="@+id/chip_all"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="All"
                android:checkable="true"
                style="@style/Widget.Material3.Chip.Filter" />

            <com.google.android.material.chip.Chip
                android:id="@+id/chip_pending"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Pending"
                android:checkable="true"
                style="@style/Widget.Material3.Chip.Filter" />

            <com.google.android.material.chip.Chip
                android:id="@+id/chip_approved"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Approved"
                android:checkable="true"
                style="@style/Widget.Material3.Chip.Filter" />

            <com.google.android.material.chip.Chip
                android:id="@+id/chip_software"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Software"
                android:checkable="true"
                style="@style/Widget.Material3.Chip.Filter" />

            <com.google.android.material.chip.Chip
                android:id="@+id/chip_network"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Network"
                android:checkable="true"
                style="@style/Widget.Material3.Chip.Filter" />

            <com.google.android.material.chip.Chip
                android:id="@+id/chip_hardware"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Hardware"
                android:checkable="true"
                style="@style/Widget.Material3.Chip.Filter" />

        </com.google.android.material.chip.ChipGroup>

    </HorizontalScrollView>

    <!-- Specialists List -->
    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipe_refresh"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recycler_view_specialists"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:padding="8dp"
                android:clipToPadding="false"
                tools:listitem="@layout/item_specialist" />

            <!-- Empty State -->
            <LinearLayout
                android:id="@+id/layout_empty_state"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:orientation="vertical"
                android:gravity="center"
                android:visibility="gone">

                <ImageView
                    android:layout_width="80dp"
                    android:layout_height="80dp"
                    android:src="@drawable/ic_specialists"
                    android:tint="@color/text_hint"
                    android:layout_marginBottom="16dp" />

                <TextView
                    android:id="@+id/tv_empty_state"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="No specialists found"
                    android:textSize="16sp"
                    android:textColor="@color/text_secondary"
                    android:gravity="center" />

            </LinearLayout>

            <!-- Loading -->
            <ProgressBar
                android:id="@+id/progress_bar"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:visibility="gone" />

        </FrameLayout>

    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

</LinearLayout>
