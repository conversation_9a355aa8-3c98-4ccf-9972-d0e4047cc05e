<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true"
    android:background="@color/background_color">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Profile Header -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="24dp"
                android:gravity="center">

                <com.google.android.material.imageview.ShapeableImageView
                    android:id="@+id/iv_profile_picture"
                    android:layout_width="80dp"
                    android:layout_height="80dp"
                    android:layout_marginBottom="16dp"
                    android:src="@drawable/ic_profile_placeholder"
                    android:scaleType="centerCrop"
                    app:shapeAppearanceOverlay="@style/CircularImageView" />

                <TextView
                    android:id="@+id/tv_specialist_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Specialist Name"
                    android:textSize="20sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:layout_marginBottom="4dp" />

                <TextView
                    android:id="@+id/tv_specialist_email"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="<EMAIL>"
                    android:textSize="14sp"
                    android:textColor="@color/text_secondary"
                    android:layout_marginBottom="8dp" />

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chip_approval_status"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="8dp"
                        android:text="Approved"
                        android:textColor="@color/white"
                        app:chipBackgroundColor="@color/success_color" />

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chip_availability_status"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Available"
                        android:textColor="@color/white"
                        app:chipBackgroundColor="@color/success_color" />

                </LinearLayout>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Specialist Information -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Specialist Information"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:layout_marginBottom="16dp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="12dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Category:"
                        android:textSize="14sp"
                        android:textColor="@color/text_secondary" />

                    <TextView
                        android:id="@+id/tv_category"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="SOFTWARE"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        android:textColor="@color/software_color" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="12dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Experience:"
                        android:textSize="14sp"
                        android:textColor="@color/text_secondary" />

                    <TextView
                        android:id="@+id/tv_experience"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="5 years"
                        android:textSize="14sp"
                        android:textColor="@color/text_primary" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="12dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Rating:"
                        android:textSize="14sp"
                        android:textColor="@color/text_secondary" />

                    <TextView
                        android:id="@+id/tv_rating"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="4.5"
                        android:textSize="14sp"
                        android:textColor="@color/text_primary" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Jobs Completed:"
                        android:textSize="14sp"
                        android:textColor="@color/text_secondary" />

                    <TextView
                        android:id="@+id/tv_jobs_completed"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="12"
                        android:textSize="14sp"
                        android:textColor="@color/text_primary" />

                </LinearLayout>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Personal Information -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Personal Information"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:layout_marginBottom="16dp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="12dp">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_email"
                        android:tint="@color/text_secondary"
                        android:layout_marginEnd="12dp" />

                    <TextView
                        android:id="@+id/tv_specialist_email_detail"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="<EMAIL>"
                        android:textSize="14sp"
                        android:textColor="@color/text_primary" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/layout_phone"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="12dp"
                    android:visibility="gone">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_phone"
                        android:tint="@color/text_secondary"
                        android:layout_marginEnd="12dp" />

                    <TextView
                        android:id="@+id/tv_specialist_phone"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="+1234567890"
                        android:textSize="14sp"
                        android:textColor="@color/text_primary" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/layout_dob"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="12dp"
                    android:visibility="gone">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_calendar"
                        android:tint="@color/text_secondary"
                        android:layout_marginEnd="12dp" />

                    <TextView
                        android:id="@+id/tv_specialist_dob"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="01/01/1990"
                        android:textSize="14sp"
                        android:textColor="@color/text_primary" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_role"
                        android:tint="@color/text_secondary"
                        android:layout_marginEnd="12dp" />

                    <TextView
                        android:id="@+id/tv_user_role"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="SPECIALIST"
                        android:textSize="14sp"
                        android:textColor="@color/text_primary" />

                </LinearLayout>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Actions -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_edit_profile"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:layout_marginBottom="12dp"
                android:text="Edit Profile"
                android:textSize="16sp"
                app:icon="@drawable/ic_edit"
                app:cornerRadius="8dp" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_logout"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:text="Logout"
                android:textSize="16sp"
                android:textColor="@color/error_color"
                app:icon="@drawable/ic_logout"
                app:iconTint="@color/error_color"
                app:cornerRadius="8dp"
                app:strokeColor="@color/error_color"
                style="@style/Widget.Material3.Button.OutlinedButton" />

        </LinearLayout>

    </LinearLayout>

</ScrollView>
