<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/card_specialist"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?android:attr/selectableItemBackground"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Header -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="12dp">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tv_specialist_id"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="ID: 123"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:layout_marginBottom="4dp" />

                <TextView
                    android:id="@+id/tv_category"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="SOFTWARE"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/software_color"
                    android:layout_marginBottom="4dp" />

                <TextView
                    android:id="@+id/tv_created_date"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Applied: Jan 01, 2024"
                    android:textSize="12sp"
                    android:textColor="@color/text_hint" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="end">

                <com.google.android.material.chip.Chip
                    android:id="@+id/chip_status"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Pending"
                    android:textColor="@color/white"
                    android:layout_marginBottom="4dp"
                    app:chipBackgroundColor="@color/warning_color" />

                <com.google.android.material.chip.Chip
                    android:id="@+id/chip_availability"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Available"
                    android:textColor="@color/white"
                    app:chipBackgroundColor="@color/success_color" />

            </LinearLayout>

        </LinearLayout>

        <!-- Stats -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="12dp">

            <TextView
                android:id="@+id/tv_experience"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="5 years experience"
                android:textSize="12sp"
                android:textColor="@color/text_secondary" />

            <TextView
                android:id="@+id/tv_rating"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Rating: 4.5"
                android:textSize="12sp"
                android:textColor="@color/text_secondary"
                android:gravity="center" />

            <TextView
                android:id="@+id/tv_jobs_completed"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="10 jobs completed"
                android:textSize="12sp"
                android:textColor="@color/text_secondary"
                android:gravity="end" />

        </LinearLayout>

        <!-- Action Buttons -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="end">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_approve"
                android:layout_width="wrap_content"
                android:layout_height="36dp"
                android:layout_marginEnd="8dp"
                android:text="Approve"
                android:textSize="12sp"
                android:textColor="@color/success_color"
                app:cornerRadius="18dp"
                app:strokeColor="@color/success_color"
                style="@style/Widget.Material3.Button.OutlinedButton" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_reject"
                android:layout_width="wrap_content"
                android:layout_height="36dp"
                android:text="Reject"
                android:textSize="12sp"
                android:textColor="@color/error_color"
                app:cornerRadius="18dp"
                app:strokeColor="@color/error_color"
                style="@style/Widget.Material3.Button.OutlinedButton" />

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
