<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/background_color">

    <!-- Header -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:gravity="center_vertical">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="My Posts"
            android:textSize="20sp"
            android:textStyle="bold"
            android:textColor="@color/text_primary" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_create_post"
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            android:text="New Post"
            android:textSize="12sp"
            app:icon="@drawable/ic_add"
            app:cornerRadius="20dp"
            style="@style/Widget.Material3.Button.OutlinedButton" />

    </LinearLayout>

    <!-- Filter Tabs -->
    <com.google.android.material.tabs.TabLayout
        android:id="@+id/tab_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="16dp"
        android:layout_marginBottom="8dp"
        app:tabMode="fixed"
        app:tabGravity="fill">

        <com.google.android.material.tabs.TabItem
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="All" />

        <com.google.android.material.tabs.TabItem
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Open" />

        <com.google.android.material.tabs.TabItem
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="In Progress" />

        <com.google.android.material.tabs.TabItem
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Completed" />

    </com.google.android.material.tabs.TabLayout>

    <!-- Posts List -->
    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipe_refresh"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recycler_view_posts"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:padding="16dp"
                android:clipToPadding="false"
                tools:listitem="@layout/item_post" />

            <!-- Empty State -->
            <LinearLayout
                android:id="@+id/layout_empty_state"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:orientation="vertical"
                android:gravity="center"
                android:visibility="gone">

                <ImageView
                    android:layout_width="80dp"
                    android:layout_height="80dp"
                    android:src="@drawable/ic_posts"
                    android:tint="@color/text_hint"
                    android:layout_marginBottom="16dp" />

                <TextView
                    android:id="@+id/tv_empty_state"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="No posts yet"
                    android:textSize="16sp"
                    android:textColor="@color/text_secondary"
                    android:gravity="center" />

            </LinearLayout>

            <!-- Loading -->
            <ProgressBar
                android:id="@+id/progress_bar"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:visibility="gone" />

        </FrameLayout>

    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

</LinearLayout>
