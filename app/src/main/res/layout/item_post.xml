<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/card_post"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?android:attr/selectableItemBackground"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Header -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tv_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Post Title"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:layout_marginBottom="4dp" />

                <TextView
                    android:id="@+id/tv_category"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="SOFTWARE"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    android:textColor="@color/software_color" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="end">

                <com.google.android.material.chip.Chip
                    android:id="@+id/chip_status"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="OPEN"
                    android:textColor="@color/white"
                    android:layout_marginBottom="4dp"
                    app:chipBackgroundColor="@color/success_color" />

                <com.google.android.material.chip.Chip
                    android:id="@+id/chip_urgent"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="URGENT"
                    android:textColor="@color/white"
                    android:visibility="gone"
                    app:chipBackgroundColor="@color/priority_urgent" />

            </LinearLayout>

        </LinearLayout>

        <!-- Description -->
        <TextView
            android:id="@+id/tv_description"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Post description goes here..."
            android:textSize="14sp"
            android:textColor="@color/text_secondary"
            android:layout_marginBottom="8dp"
            android:maxLines="3"
            android:ellipsize="end" />

        <!-- Details -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginBottom="8dp">

            <TextView
                android:id="@+id/tv_priority"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Priority: HIGH"
                android:textSize="12sp"
                android:textColor="@color/text_secondary"
                android:layout_marginBottom="2dp" />

            <TextView
                android:id="@+id/tv_budget"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Budget: $100 - $500"
                android:textSize="12sp"
                android:textColor="@color/text_secondary"
                android:layout_marginBottom="2dp"
                android:visibility="gone" />

            <TextView
                android:id="@+id/tv_location"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Location: New York"
                android:textSize="12sp"
                android:textColor="@color/text_secondary"
                android:layout_marginBottom="2dp"
                android:visibility="gone" />

            <TextView
                android:id="@+id/tv_created_date"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Posted: Jan 01, 2024"
                android:textSize="12sp"
                android:textColor="@color/text_hint" />

        </LinearLayout>

        <!-- Stats -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="12dp">

            <TextView
                android:id="@+id/tv_views_count"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="25 views"
                android:textSize="12sp"
                android:textColor="@color/text_hint"
                android:drawableStart="@drawable/ic_visibility"
                android:drawablePadding="4dp"
                android:gravity="center_vertical" />

            <TextView
                android:id="@+id/tv_applications_count"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="5 applications"
                android:textSize="12sp"
                android:textColor="@color/text_hint"
                android:drawableStart="@drawable/ic_applications"
                android:drawablePadding="4dp"
                android:gravity="center_vertical|end" />

        </LinearLayout>

        <!-- Admin Action Buttons -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="end"
            android:visibility="gone">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_change_status"
                android:layout_width="wrap_content"
                android:layout_height="36dp"
                android:layout_marginEnd="8dp"
                android:text="Change Status"
                android:textSize="12sp"
                app:cornerRadius="18dp"
                style="@style/Widget.Material3.Button.OutlinedButton" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_delete"
                android:layout_width="wrap_content"
                android:layout_height="36dp"
                android:text="Delete"
                android:textSize="12sp"
                android:textColor="@color/error_color"
                app:cornerRadius="18dp"
                app:strokeColor="@color/error_color"
                style="@style/Widget.Material3.Button.OutlinedButton" />

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
