<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/card_user"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?android:attr/selectableItemBackground"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- User Info -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="12dp">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tv_user_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="User Name"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:layout_marginBottom="4dp" />

                <TextView
                    android:id="@+id/tv_user_email"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="<EMAIL>"
                    android:textSize="14sp"
                    android:textColor="@color/text_secondary"
                    android:layout_marginBottom="4dp" />

                <TextView
                    android:id="@+id/tv_created_date"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Joined: Jan 01, 2024"
                    android:textSize="12sp"
                    android:textColor="@color/text_hint" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="end">

                <com.google.android.material.chip.Chip
                    android:id="@+id/chip_status"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Active"
                    android:textColor="@color/white"
                    android:layout_marginBottom="4dp"
                    app:chipBackgroundColor="@color/success_color" />

                <TextView
                    android:id="@+id/tv_user_role"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="USER"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    android:textColor="@color/primary_color"
                    android:background="@drawable/role_background"
                    android:padding="4dp" />

            </LinearLayout>

        </LinearLayout>

        <!-- Action Buttons -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="end">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_toggle_status"
                android:layout_width="wrap_content"
                android:layout_height="36dp"
                android:layout_marginEnd="8dp"
                android:text="Deactivate"
                android:textSize="12sp"
                app:cornerRadius="18dp"
                style="@style/Widget.Material3.Button.OutlinedButton" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_delete"
                android:layout_width="wrap_content"
                android:layout_height="36dp"
                android:text="Delete"
                android:textSize="12sp"
                android:textColor="@color/error_color"
                app:cornerRadius="18dp"
                app:strokeColor="@color/error_color"
                style="@style/Widget.Material3.Button.OutlinedButton" />

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
