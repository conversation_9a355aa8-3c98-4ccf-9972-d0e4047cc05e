plugins {
    alias(libs.plugins.android.application)
    id 'com.google.gms.google-services'
}

android {
    namespace 'com.example.simaportal'
    compileSdk 36

    defaultConfig {
        applicationId "com.example.simaportal"
        minSdk 24
        targetSdk 36
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }

    buildFeatures {
        viewBinding true
    }
}

dependencies {
    // Core Android
    implementation libs.appcompat
    implementation libs.material
    implementation libs.activity
    implementation libs.constraintlayout
    implementation libs.cardview
    implementation libs.recyclerview
    implementation libs.viewpager2
    implementation libs.fragment

    // Room Database
    implementation libs.room.runtime
    implementation libs.room.ktx
    annotationProcessor libs.room.compiler

    // Lifecycle
    implementation libs.lifecycle.viewmodel
    implementation libs.lifecycle.livedata
    implementation libs.lifecycle.common.java8

    // Navigation
    implementation libs.navigation.fragment
    implementation libs.navigation.ui

    // Networking
    implementation libs.retrofit
    implementation libs.retrofit.gson
    implementation libs.okhttp
    implementation libs.okhttp.logging

    // Image Loading
    implementation libs.glide
    annotationProcessor libs.glide.compiler

    // Firebase
    implementation platform(libs.firebase.bom)
    implementation libs.firebase.auth
    implementation libs.firebase.firestore
    implementation libs.firebase.storage
    implementation libs.firebase.messaging

    // JSON
    implementation libs.gson

    // Testing
    testImplementation libs.junit
    androidTestImplementation libs.ext.junit
    androidTestImplementation libs.espresso.core
}