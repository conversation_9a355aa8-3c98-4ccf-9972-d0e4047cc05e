plugins {
    alias(libs.plugins.android.application)
    id 'com.google.gms.google-services'
}

android {
    namespace 'com.example.simaportal'
    compileSdk 34

    defaultConfig {
        applicationId "com.example.simaportal"
        minSdk 24
        targetSdk 34
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"

        // Room schema export directory
        javaCompileOptions {
            annotationProcessorOptions {
                arguments += ["room.schemaLocation": "$projectDir/schemas".toString()]
            }
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    buildFeatures {
        viewBinding true
        dataBinding true
    }

    packagingOptions {
        exclude 'META-INF/DEPENDENCIES'
        exclude 'META-INF/LICENSE'
        exclude 'META-INF/LICENSE.txt'
        exclude 'META-INF/license.txt'
        exclude 'META-INF/NOTICE'
        exclude 'META-INF/NOTICE.txt'
        exclude 'META-INF/notice.txt'
        exclude 'META-INF/ASL2.0'
        exclude("META-INF/*.kotlin_module")
    }
}

dependencies {
    // Core Android
    implementation libs.appcompat
    implementation libs.material
    implementation libs.activity
    implementation libs.constraintlayout
    implementation libs.cardview
    implementation libs.recyclerview
    implementation libs.viewpager2
    implementation libs.fragment

    // Room Database
    implementation libs.room.runtime
    implementation libs.room.ktx
    annotationProcessor libs.room.compiler

    // Lifecycle
    implementation libs.lifecycle.viewmodel
    implementation libs.lifecycle.livedata
    implementation libs.lifecycle.common.java8

    // Navigation
    implementation libs.navigation.fragment
    implementation libs.navigation.ui

    // Networking
    implementation libs.retrofit
    implementation libs.retrofit.gson
    implementation libs.okhttp
    implementation libs.okhttp.logging

    // Image Loading
    implementation libs.glide
    annotationProcessor libs.glide.compiler

    // Firebase
    implementation platform(libs.firebase.bom)
    implementation libs.firebase.auth
    implementation libs.firebase.firestore
    implementation libs.firebase.storage
    implementation libs.firebase.messaging

    // JSON
    implementation libs.gson

    // Additional UI Components
    implementation libs.swiperefreshlayout
    implementation libs.coordinatorlayout

    // For date/time handling
    implementation libs.core

    // Testing
    testImplementation libs.junit
    androidTestImplementation libs.ext.junit
    androidTestImplementation libs.espresso.core
}